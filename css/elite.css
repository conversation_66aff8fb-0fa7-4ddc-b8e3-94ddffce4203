/* 
 * Elite Portfolio 2.0 - Custom Styles
 * Thibaut <PERSON> - Développeur Full-Stack Elite
 * Optimisé pour la performance et la conversion
 */

/* Variables CSS personnalisées */
:root {
    --elite-orange: #f97316;
    --elite-red: #dc2626;
    --elite-gradient: linear-gradient(135deg, #f97316 0%, #dc2626 100%);
    --elite-shadow: 0 25px 50px -12px rgba(249, 115, 22, 0.25);
    --elite-glow: 0 0 30px rgba(249, 115, 22, 0.3);
}

/* Smooth scrolling amélioré */
html {
    scroll-behavior: smooth;
    scroll-padding-top: 100px;
}

/* Navigation styles */
.nav-link {
    position: relative;
    color: #d1d5db;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-link:hover {
    color: #f97316;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--elite-gradient);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

/* Animations personnalisées */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@keyframes bounce-slow {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-25px); }
}

@keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 20px rgba(249, 115, 22, 0.3); }
    50% { box-shadow: 0 0 40px rgba(249, 115, 22, 0.6); }
}

/* Effets de hover pour les cartes */
.hover-lift {
    transition: all 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: var(--elite-shadow);
}

/* Portfolio filter buttons */
.portfolio-filter.active {
    background: var(--elite-gradient);
    color: white;
    border-color: transparent;
}

/* Formulaire de contact amélioré */
#contact-form input:focus,
#contact-form select:focus,
#contact-form textarea:focus {
    outline: none;
    border-color: #f97316;
    box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
}

/* Animations d'apparition */
.fade-in-up {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in-up.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Effets de brillance */
.shine-effect {
    position: relative;
    overflow: hidden;
}

.shine-effect::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.shine-effect:hover::before {
    left: 100%;
}

/* Responsive design amélioré */
@media (max-width: 768px) {
    .hero h1 {
        font-size: 2.5rem !important;
    }
    
    .hero .stats {
        grid-template-columns: 1fr !important;
        gap: 1rem !important;
    }
    
    .portfolio-grid {
        grid-template-columns: 1fr !important;
    }
    
    .services-grid {
        grid-template-columns: 1fr !important;
    }
}

/* Optimisations performance */
.will-change-transform {
    will-change: transform;
}

.backface-hidden {
    backface-visibility: hidden;
}

/* Accessibilité */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles pour accessibilité */
button:focus-visible,
a:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible {
    outline: 2px solid #f97316;
    outline-offset: 2px;
}

/* Loading states */
.loading {
    opacity: 0.7;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f97316;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success states */
.success-message {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-top: 1rem;
    display: none;
}

.success-message.show {
    display: block;
    animation: slideInUp 0.5s ease;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Error states */
.error-message {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-top: 1rem;
    display: none;
}

.error-message.show {
    display: block;
    animation: slideInUp 0.5s ease;
}
