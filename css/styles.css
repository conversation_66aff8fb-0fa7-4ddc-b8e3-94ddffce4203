/* ==========================================================================
   THIBAUT CAMPANA - PORTFOLIO CSS MODERNE AVEC EFFETS PREMIUM
   Version: 3.0 - ÉDITION EFFECTS
   Description: Styles avancés avec effets Shine, 3D, Glow et Gradients animés
   ========================================================================== */

/* ===== RESET & BASE ===== */
*,
*::before,
*::after {
    box-sizing: border-box;
}

:root {
    /* Couleurs principales */
    --color-primary-50: #fff7ed;
    --color-primary-100: #ffedd5;
    --color-primary-500: #f97316;
    --color-primary-600: #ea580c;
    --color-primary-700: #dc2626;
    --color-primary-900: #7c2d12;
    
    /* Couleurs fonctionnelles */
    --color-success: #10b981;
    --color-warning: #f59e0b;
    --color-error: #ef4444;
    --color-info: #3b82f6;
    
    /* Gradients Premium */
    --gradient-primary: linear-gradient(135deg, #f97316, #ea580c, #dc2626);
    --gradient-shine: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.15) 50%, transparent 70%);
    --gradient-glow: linear-gradient(45deg, #f97316, #ea580c, #dc2626, #f97316);
    
    /* Border Gradients */
    --border-primary: linear-gradient(45deg, #f97316, #ea580c);
    --border-glow: linear-gradient(45deg, rgba(249,115,22,0.8), rgba(234,88,12,0.8));
    
    /* Typographie */
    --font-primary: 'Space Grotesk', sans-serif;
    --font-mono: 'JetBrains Mono', monospace;
    
    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    
    /* Transitions */
    --transition-fast: 0.15s ease-out;
    --transition-normal: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;
    --transition-shine: 0.6s ease-out;
    
    /* Shadows Premium */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
    --shadow-glow: 0 0 20px rgba(249, 115, 22, 0.2);
    --shadow-glow-strong: 0 0 30px rgba(249, 115, 22, 0.3);
    
    /* 3D Transform values */
    --transform-3d-hover: translateY(-6px) scale(1.01);
    --transform-3d-card: translateY(-8px) scale(1.02);
    
    /* Breakpoints */
    --bp-sm: 640px;
    --bp-md: 768px;
    --bp-lg: 1024px;
    --bp-xl: 1280px;
}

/* ===== ANIMATIONS PREMIUM ===== */
@keyframes shine {
    0% {
        left: -100%;
        opacity: 0;
    }
    25% {
        opacity: 0.3;
    }
    50% {
        left: 100%;
        opacity: 0;
    }
    75% {
        opacity: 0.2;
    }
    100% {
        left: -100%;
        opacity: 0;
    }
}

@keyframes shineHover {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    50% {
        transform: translateX(100%);
        opacity: 0.4;
    }
    100% {
        transform: translateX(-100%);
        opacity: 0;
    }
}

@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

@keyframes gradientRotate {
    0% {
        background-position: 0% 50%;
    }
    25% {
        background-position: 100% 50%;
    }
    50% {
        background-position: 100% 100%;
    }
    75% {
        background-position: 0% 100%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes glowPulse {
    0%, 100% {
        box-shadow: 0 0 15px rgba(249, 115, 22, 0.2);
    }
    50% {
        box-shadow: 0 0 25px rgba(249, 115, 22, 0.4);
    }
}

@keyframes borderRotate {
    0% {
        border-image-source: linear-gradient(0deg, #f97316, #ea580c, #dc2626);
    }
    33% {
        border-image-source: linear-gradient(120deg, #f97316, #ea580c, #dc2626);
    }
    66% {
        border-image-source: linear-gradient(240deg, #f97316, #ea580c, #dc2626);
    }
    100% {
        border-image-source: linear-gradient(360deg, #f97316, #ea580c, #dc2626);
    }
}

/* ===== EFFETS SHINE ===== */
.shine-effect {
    position: relative;
    overflow: hidden;
}

.shine-effect::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-shine);
    transition: all var(--transition-shine);
    z-index: 1;
    pointer-events: none;
}

.shine-effect:hover::before {
    animation: shineHover 1.2s ease-out;
}

.shine-auto {
    position: relative;
    overflow: hidden;
}

.shine-auto::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-shine);
    animation: shine 6s ease-in-out infinite;
    z-index: 1;
    pointer-events: none;
}

/* ===== TRANSFORMATIONS 3D ===== */
.transform-3d {
    perspective: 1000px;
    transform-style: preserve-3d;
    transition: all var(--transition-normal);
}

.transform-3d:hover {
    transform: var(--transform-3d-hover);
}

.card-3d {
    perspective: 1000px;
    transform-style: preserve-3d;
    transition: all var(--transition-slow);
}

.card-3d:hover {
    transform: var(--transform-3d-card);
}

.rotate-3d {
    transition: all var(--transition-slow);
}

.rotate-3d:hover {
    transform: rotateY(10deg) rotateX(10deg) scale(1.05);
}

/* ===== EFFETS GLOW ET BRILLANCE ===== */
.glow-effect {
    transition: all var(--transition-normal);
}

.glow-effect:hover {
    box-shadow: var(--shadow-glow);
}

.glow-strong {
    transition: all var(--transition-normal);
}

.glow-strong:hover {
    box-shadow: var(--shadow-glow-strong);
}

.glow-pulse {
    animation: glowPulse 2s ease-in-out infinite;
}

.border-glow {
    border: 2px solid;
    border-image: var(--border-glow) 1;
    transition: all var(--transition-normal);
}

.border-glow:hover {
    border-image: linear-gradient(45deg, #f97316, #ea580c, #dc2626, #f97316) 1;
    box-shadow: var(--shadow-glow);
}

.border-animated {
    border: 2px solid;
    border-image: linear-gradient(45deg, #f97316, #ea580c, #dc2626) 1;
    animation: borderRotate 3s linear infinite;
}

/* ===== GRADIENTS ANIMÉS ===== */
.gradient-animated {
    background: var(--gradient-glow);
    background-size: 400% 400%;
    animation: gradientShift 3s ease-in-out infinite;
}

.gradient-rotate {
    background: var(--gradient-glow);
    background-size: 400% 400%;
    animation: gradientRotate 4s ease-in-out infinite;
}

.gradient-text {
    background: var(--gradient-primary);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
}

/* ===== UTILITAIRES ANIMÉS ===== */
.animate-float {
    animation: float 6s ease-in-out infinite;
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-scale-in {
    animation: scaleIn 0.5s ease-out;
}

.animate-pulse-slow {
    animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce-slow {
    animation: bounce 3s infinite;
}

/* ===== NAVIGATION PREMIUM ===== */
.nav-link {
    @apply text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium;
    position: relative;
    overflow: hidden;
    transition: all var(--transition-normal);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: all var(--transition-normal);
    transform: translateX(-50%);
}

.nav-link:hover::after {
    width: 100%;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-shine);
    transition: all var(--transition-shine);
    z-index: -1;
}

.nav-link:hover::before {
    left: 100%;
}

/* Navbar avec glassmorphism */
#navbar {
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    background-color: rgba(15, 23, 42, 0.8);
    border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

/* Menu Mobile Premium - SOLUTION SILICON VALLEY */
#mobile-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 50;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-top: 1px solid rgba(249, 115, 22, 0.2);
    background: rgba(30, 41, 59, 0.98);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transform: translateY(-20px);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* États du menu - SOLUTION ROBUSTE */
.mobile-menu-hidden {
    transform: translateY(-20px) !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
}

.mobile-menu-visible {
    transform: translateY(0) !important;
    opacity: 1 !important;
    visibility: visible !important;
    pointer-events: auto !important;
}

/* Liens du menu mobile - AMÉLIORÉS */
.mobile-menu-link {
    position: relative;
    overflow: hidden;
    text-decoration: none;
    display: flex;
    align-items: center;
    font-weight: 500;
    letter-spacing: 0.025em;
}

.mobile-menu-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(249, 115, 22, 0.1), rgba(234, 88, 12, 0.1));
    transition: left 0.3s ease;
    z-index: -1;
}

.mobile-menu-link:hover::before {
    left: 0;
}

.mobile-menu-link:active {
    transform: scale(0.98);
}

#mobile-menu a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-shine);
    transition: left 0.4s ease;
    z-index: -1;
}

#mobile-menu a:hover::before {
    left: 0;
}

#mobile-menu a:hover {
    background: rgba(249, 115, 22, 0.1);
    transform: translateX(8px);
}

#mobile-menu-toggle {
    transition: all 0.3s ease;
    border-radius: 8px;
    background: rgba(249, 115, 22, 0.1);
}

#mobile-menu-toggle:hover {
    background: rgba(249, 115, 22, 0.2);
    transform: scale(1.1);
}

#mobile-menu-toggle:active {
    transform: scale(0.95);
}

/* ===== BOUTONS PREMIUM ===== */
.btn-primary {
    @apply text-white px-6 py-3 rounded-full font-semibold;
    background: var(--gradient-primary);
    background-size: 400% 400%;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    animation: gradientShift 3s ease-in-out infinite;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-shine);
    transition: left var(--transition-shine);
    z-index: 1;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-4px) scale(1.05);
    box-shadow: var(--shadow-glow-strong);
}

.btn-secondary {
    @apply text-orange-500 px-6 py-3 rounded-full font-semibold;
    border: 2px solid;
    border-image: var(--border-primary) 1;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.btn-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    transition: left var(--transition-shine);
    z-index: -1;
}

.btn-secondary:hover::before {
    left: 0;
}

.btn-secondary:hover {
    @apply text-white;
    transform: translateY(-4px) scale(1.05);
    box-shadow: var(--shadow-glow);
}

/* ===== CARDS PREMIUM ===== */
.card {
    @apply bg-slate-800/80 backdrop-blur-sm rounded-2xl border border-slate-700/50;
    transition: all var(--transition-slow);
    position: relative;
    overflow: hidden;
    perspective: 1000px;
    transform-style: preserve-3d;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.card::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-shine);
    transition: left var(--transition-shine);
    z-index: 1;
    pointer-events: none;
}

.card:hover::before {
    opacity: 1;
}

.card:hover::after {
    left: 100%;
}

.card:hover {
    transform: translateY(-8px) scale(1.02);
    border-color: rgba(249, 115, 22, 0.6);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2), 
                0 20px 50px rgba(249, 115, 22, 0.3),
                0 0 30px rgba(249, 115, 22, 0.2);
}

/* ===== PORTFOLIO PREMIUM ===== */
.portfolio-filter {
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.portfolio-filter::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-shine);
    transition: left var(--transition-shine);
    z-index: 1;
}

.portfolio-filter:hover::before {
    left: 100%;
}

.portfolio-filter.active {
    background: var(--gradient-primary);
    @apply text-white border-orange-500;
    animation: gradientShift 3s ease-in-out infinite;
    background-size: 400% 400%;
}

.portfolio-filter:not(.active):hover {
    @apply border-orange-500 text-orange-500;
    transform: translateY(-4px) scale(1.05);
    box-shadow: var(--shadow-glow);
}

/* ===== PORTFOLIO ITEMS PREMIUM ===== */
.portfolio-item {
    transition: all var(--transition-slow);
    will-change: transform;
    perspective: 1000px;
    transform-style: preserve-3d;
    position: relative;
    overflow: hidden;
}

.portfolio-item::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-shine);
    transition: left var(--transition-shine);
    z-index: 2;
    pointer-events: none;
}

.portfolio-item:hover::after {
    left: 100%;
}

.portfolio-item:hover {
    transform: translateY(-12px) scale(1.03);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3), 
                0 25px 60px rgba(249, 115, 22, 0.4),
                0 0 40px rgba(249, 115, 22, 0.3);
}

.portfolio-item img {
    transition: transform var(--transition-slow);
}

.portfolio-item:hover img {
    transform: scale(1.08);
}

/* ===== AVATAR PREMIUM ===== */
.avatar-premium {
    position: relative;
    overflow: hidden;
    border-radius: 50%;
    background: var(--gradient-primary);
    background-size: 400% 400%;
    animation: gradientShift 3s ease-in-out infinite;
    padding: 4px;
}

.avatar-premium::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-shine);
    border-radius: 50%;
    transition: left var(--transition-shine);
    z-index: 1;
}

.avatar-premium:hover::before {
    animation: shineHover 0.8s ease-out;
}

.avatar-premium:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-glow);
}

/* ===== COMPÉTENCES PREMIUM ===== */
.skill-card {
    @apply bg-slate-800/50 p-4 rounded-lg border border-slate-700/50;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    perspective: 1000px;
}

.skill-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-shine);
    transition: left var(--transition-shine);
    z-index: 1;
    pointer-events: none;
}

.skill-card:hover::after {
    left: 100%;
}

.skill-card:hover {
    transform: translateY(-8px) scale(1.05) rotateX(5deg);
    border-image: var(--border-glow) 1;
    box-shadow: var(--shadow-glow);
}

/* ===== FORMULAIRES PREMIUM ===== */
.form-input {
    @apply w-full px-4 py-3 bg-slate-700/50 border border-slate-600 rounded-lg text-white placeholder-gray-400;
    transition: all var(--transition-normal);
    position: relative;
}

.form-input:focus {
    @apply outline-none;
    border-image: var(--border-glow) 1;
    box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.2), var(--shadow-glow);
}

.form-input:invalid {
    @apply border-red-500;
}

.form-input:valid {
    @apply border-emerald-500;
}

/* ===== GLASS MORPHISM PREMIUM ===== */
.glass {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.glass::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-shine);
    transition: left var(--transition-shine);
    z-index: 1;
    pointer-events: none;
}

.glass:hover::before {
    left: 100%;
}

/* ===== LOADING STATES PREMIUM ===== */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-shine);
    animation: shine 1.5s infinite;
}

/* ===== SCROLL BEHAVIOR ===== */
html {
    scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #1e293b;
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gradient-glow);
}

/* ===== ACCESSIBILITÉ ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .card:hover {
        transform: translateY(-8px) scale(1.02);
    }
    
    .portfolio-item:hover {
        transform: translateY(-10px) scale(1.03);
    }
    
    .transform-3d:hover {
        transform: translateY(-6px) scale(1.02);
    }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* ===== MICRO-INTERACTIONS PREMIUM ===== */
.hover-lift-3d {
    transition: all var(--transition-normal);
    perspective: 1000px;
}

.hover-lift-3d:hover {
    transform: translateY(-8px) rotateX(5deg) scale(1.02);
}

.hover-glow-premium {
    transition: all var(--transition-normal);
}

.hover-glow-premium:hover {
    box-shadow: var(--shadow-glow-strong);
    transform: scale(1.05);
}

/* ===== UTILITIES PREMIUM ===== */
.text-gradient-animated {
    background: var(--gradient-glow);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
}

.border-gradient-animated {
    border: 2px solid;
    border-image: var(--gradient-glow) 1;
    background-size: 400% 400%;
    animation: gradientShift 3s ease-in-out infinite;
}

/* ===== TERMINAL CODE PREMIUM ===== */
.terminal {
    background: rgba(15, 23, 42, 0.9);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.4), 
                0 0 30px rgba(249, 115, 22, 0.2);
    border: 1px solid rgba(249, 115, 22, 0.3);
    max-width: 700px;
    width: 100%;
    margin: 0 auto;
    position: relative;
    animation: terminalGlow 3s ease-in-out infinite;
}

@keyframes terminalGlow {
    0%, 100% {
        box-shadow: 0 25px 60px rgba(0, 0, 0, 0.4), 
                    0 0 30px rgba(249, 115, 22, 0.2);
    }
    50% {
        box-shadow: 0 25px 60px rgba(0, 0, 0, 0.4), 
                    0 0 40px rgba(249, 115, 22, 0.4);
    }
}

.terminal-header {
    background: rgba(30, 41, 59, 0.8);
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border-bottom: 1px solid rgba(249, 115, 22, 0.2);
}

.terminal-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    box-shadow: 0 0 10px currentColor;
}

.dot-red { 
    background: #ff5f56; 
    color: #ff5f56;
}
.dot-yellow { 
    background: #ffbd2e; 
    color: #ffbd2e;
}
.dot-green { 
    background: #27c93f; 
    color: #27c93f;
}

.terminal-content {
    padding: 2.5rem;
    font-family: 'JetBrains Mono', monospace;
    font-size: 1rem;
    line-height: 1.9;
    min-height: 350px;
    position: relative;
    overflow: hidden;
    color: #e4e4e7;
}

.typing-cursor {
    background: #f97316;
    color: #f97316;
    animation: blink 1s infinite;
    padding: 0 2px;
    margin-left: 2px;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

.code-comment { 
    color: #6b7280; 
    font-style: italic; 
}
.code-keyword { 
    color: #f97316; 
    font-weight: 600; 
}
.code-string { 
    color: #10b981; 
}
.code-function { 
    color: #3b82f6; 
    font-weight: 500; 
}
.code-variable { 
    color: #ea580c; 
    font-weight: 500;
}
.code-number {
    color: #a855f7;
}

/* Terminal Responsive */
@media (max-width: 768px) {
    .terminal {
        max-width: 95%;
        margin: 1rem auto;
    }
    
    .terminal-content {
        padding: 1.5rem;
        font-size: 0.9rem;
        min-height: 280px;
    }
    
    .terminal-header {
        padding: 0.8rem;
    }
}

/* ===== MODALE DE CONFIRMATION PREMIUM ===== */
/* Suivant les spécifications : Mobile First, Accessibility, Animations premium */

/* Overlay principal - Mobile First */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    opacity: 1;
    visibility: visible;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-overlay.hidden {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}

/* Container principal - Responsive */
.modal-container {
    width: 100%;
    max-width: 400px;
    transform: scale(1) translateY(0);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-overlay.hidden .modal-container {
    transform: scale(0.9) translateY(20px);
}

/* Contenu de la modale */
.modal-content {
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 24px;
    padding: 2rem;
    text-align: center;
    border: 1px solid rgba(249, 115, 22, 0.2);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4),
                0 0 40px rgba(249, 115, 22, 0.2);
    position: relative;
    overflow: hidden;
}

/* Effet shine sur la modale */
.modal-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-shine);
    transition: left 0.6s ease;
    z-index: 1;
    pointer-events: none;
}

.modal-content:hover::before {
    left: 100%;
}

/* Icône de succès */
.success-icon {
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 2;
}

.check-circle {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    animation: checkCircleScale 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 0 30px rgba(249, 115, 22, 0.4);
}

@keyframes checkCircleScale {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.check-mark {
    width: 36px;
    height: 36px;
    color: white;
}

.check-path {
    stroke-dasharray: 24;
    stroke-dashoffset: 24;
    animation: checkmarkDraw 0.8s ease-out 0.3s forwards;
}

@keyframes checkmarkDraw {
    to {
        stroke-dashoffset: 0;
    }
}

/* Titre de la modale */
.modal-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
    background: var(--gradient-primary);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
    position: relative;
    z-index: 2;
}

/* Message de la modale */
.modal-message {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    position: relative;
    z-index: 2;
}

.modal-message strong {
    color: #f97316;
    font-weight: 600;
}

/* Actions / Boutons */
.modal-actions {
    position: relative;
    z-index: 2;
}

.btn-modal-close {
    background: var(--gradient-primary);
    background-size: 400% 400%;
    animation: gradientShift 3s ease-in-out infinite;
    color: white;
    border: none;
    border-radius: 12px;
    padding: 0.875rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-width: 120px;
}

.btn-modal-close::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-shine);
    transition: left 0.4s ease;
    z-index: 1;
}

.btn-modal-close:hover::before {
    left: 100%;
}

.btn-modal-close:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 10px 30px rgba(249, 115, 22, 0.4);
}

.btn-modal-close:active {
    transform: translateY(0) scale(0.98);
}

.btn-modal-close:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.3);
}

/* Loading state du bouton de formulaire */
.btn-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.8;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    top: 50%;
    left: 50%;
    margin-left: -10px;
    margin-top: -10px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* States du formulaire */
.form-success {
    border-color: #10b981 !important;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
}

.form-error {
    border-color: #ef4444 !important;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

/* Message d'erreur */
.error-message {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #ef4444;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-size: 0.9rem;
    margin-top: 1rem;
    display: none;
}

.error-message.show {
    display: block;
    animation: fadeInUp 0.3s ease-out;
}

/* Responsive - Tablette */
@media (min-width: 768px) {
    .modal-container {
        max-width: 460px;
    }
    
    .modal-content {
        padding: 2.5rem;
    }
    
    .check-circle {
        width: 96px;
        height: 96px;
    }
    
    .check-mark {
        width: 42px;
        height: 42px;
    }
    
    .modal-title {
        font-size: 1.75rem;
    }
    
    .modal-message {
        font-size: 1.1rem;
    }
}

/* Responsive - Desktop */
@media (min-width: 1024px) {
    .modal-container {
        max-width: 520px;
    }
    
    .modal-content {
        padding: 3rem;
    }
    
    .check-circle {
        width: 120px;
        height: 120px;
    }
    
    .check-mark {
        width: 48px;
        height: 48px;
    }
    
    .modal-title {
        font-size: 2rem;
    }
}

/* Accessibility - Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .modal-overlay,
    .modal-container,
    .check-circle,
    .check-path,
    .btn-modal-close {
        animation: none !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus trap styles */
.modal-overlay[aria-modal="true"] {
    isolation: isolate;
}

/* ===== 🚀 CORRECTIONS MENU MOBILE SILICON VALLEY ===== */

/* Menu mobile - États simplifiés et robustes */
#mobile-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 50;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-top: 1px solid rgba(249, 115, 22, 0.2);
    background: rgba(30, 41, 59, 0.98);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    /* Transition gérée par JavaScript pour éviter les conflits */
}

/* États du menu - Simplifiés pour éviter les bugs */
#mobile-menu.hidden {
    display: none !important;
    opacity: 0 !important;
    transform: translateY(-20px) !important;
}

#mobile-menu:not(.hidden) {
    display: block !important;
}

/* Liens du menu mobile - Améliorés */
#mobile-menu a {
    display: block;
    padding: 0.75rem 1rem;
    margin: 0.25rem;
    border-radius: 8px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    text-decoration: none;
    color: #d1d5db;
}

#mobile-menu a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(249, 115, 22, 0.1), rgba(234, 88, 12, 0.1));
    transition: left 0.3s ease;
    z-index: -1;
}

#mobile-menu a:hover::before {
    left: 0;
}

#mobile-menu a:hover {
    background: rgba(249, 115, 22, 0.1);
    color: #f97316;
    transform: translateX(8px);
}

#mobile-menu a:active {
    transform: translateX(4px) scale(0.98);
}

/* Toggle button - Amélioré */
#mobile-menu-toggle {
    padding: 0.5rem;
    border-radius: 8px;
    background: rgba(249, 115, 22, 0.05);
    border: 1px solid rgba(249, 115, 22, 0.1);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    outline: none;
    position: relative;
    overflow: hidden;
}

#mobile-menu-toggle::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(249, 115, 22, 0.1);
    transition: left 0.3s ease;
    z-index: -1;
}

#mobile-menu-toggle:hover::before {
    left: 0;
}

#mobile-menu-toggle:hover {
    background: rgba(249, 115, 22, 0.15);
    border-color: rgba(249, 115, 22, 0.3);
    transform: scale(1.05);
}

#mobile-menu-toggle:active {
    transform: scale(0.95);
}

#mobile-menu-toggle:focus {
    box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.2);
    outline: none;
}

/* Animation de rotation pour l'icône hamburger */
#mobile-menu-toggle i {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#mobile-menu-toggle[aria-expanded="true"] i {
    transform: rotate(90deg);
}

/* Micro-animations pour les liens du menu */
.mobile-menu-link {
    transform: translateX(-10px);
    opacity: 0;
    animation: slideInLeft 0.3s ease-out forwards;
}

.mobile-menu-link:nth-child(1) { animation-delay: 0.1s; }
.mobile-menu-link:nth-child(2) { animation-delay: 0.15s; }
.mobile-menu-link:nth-child(3) { animation-delay: 0.2s; }
.mobile-menu-link:nth-child(4) { animation-delay: 0.25s; }
.mobile-menu-link:nth-child(5) { animation-delay: 0.3s; }
.mobile-menu-link:nth-child(6) { animation-delay: 0.35s; }

@keyframes slideInLeft {
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Overlay premium */
#mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    z-index: 40;
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Prévention des bugs de scroll - SOLUTION SILICON VALLEY */
body.menu-scroll-locked {
    overflow: hidden !important;
    position: fixed !important;
    width: 100% !important;
    height: 100% !important;
}

/* Correction pour iOS Safari */
@supports (-webkit-touch-callout: none) {
    body.menu-scroll-locked {
        -webkit-overflow-scrolling: touch;
        position: fixed !important;
    }
}

/* Navbar amélioration responsive */
@media (max-width: 767px) {
    #navbar {
        padding: 0 1rem;
    }
    
    #navbar .flex {
        padding: 0;
    }
    
    #mobile-menu {
        margin: 0 -1rem;
        border-radius: 0;
        border-left: none;
        border-right: none;
    }
}

/* Performance optimizations */
#mobile-menu,
#mobile-menu-toggle,
#mobile-menu-overlay {
    will-change: transform, opacity;
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    #mobile-menu,
    #mobile-menu-toggle,
    #mobile-menu a,
    #mobile-menu-overlay {
        transition: none !important;
        animation: none !important;
    }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
    #mobile-menu a:hover {
        background: rgba(249, 115, 22, 0.1);
        transform: none;
    }
    
    #mobile-menu-toggle:hover {
        transform: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    #mobile-menu {
        border: 2px solid #f97316;
        background: #1e293b;
    }
    
    #mobile-menu a {
        border: 1px solid transparent;
    }
    
    #mobile-menu a:hover {
        border-color: #f97316;
        background: #374151;
    }
}

/* ===== CORRECTIONS SUPPLÉMENTAIRES ===== */

/* Z-index hierarchy correction */
#navbar {
    z-index: 50;
}

#mobile-menu {
    z-index: 45;
}

#mobile-menu-overlay {
    z-index: 40;
}

/* Prevent text selection during animations */
#mobile-menu-toggle,
#mobile-menu {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Fix for iOS Safari */
@supports (-webkit-touch-callout: none) {
    #mobile-menu {
        -webkit-backdrop-filter: blur(20px);
    }
    
    #mobile-menu-overlay {
        -webkit-backdrop-filter: blur(8px);
    }
}

/* ===== SUCCESS INDICATORS ===== */
.navigation-ready {
    opacity: 1 !important;
    transform: translateY(0) !important;
}

/* Debug helpers (remove in production) */
.debug-menu #mobile-menu {
    border: 2px solid red !important;
}

.debug-menu #mobile-menu-toggle {
    border: 2px solid blue !important;
}

.debug-menu #mobile-menu-overlay {
    background: rgba(255, 0, 0, 0.3) !important;
}
