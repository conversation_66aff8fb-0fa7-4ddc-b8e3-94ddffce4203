<!DOCTYPE html>
<html lang="fr" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ThibautDev | Développeur Full-Stack Expert IA qui fait CONVERTIR - Marrakech 🚀</title>
    
    <!-- SEO Optimisé -->
    <meta name="description" content="🎯 ThibautDev - Développeur Full-Stack Expert IA à Marrakech. Sites web premium qui CONVERTISSENT avec IA intégrée. Client #1 Google 'golf marrakech'. Résultats garantis. Devis gratuit 24h.">
    <meta name="keywords" content="ThibautDev, développeur web IA Marrakech, site web premium, IA intégrée, développement web Marrakech, expert IA, #1 Google golf marrakech, développeur full-stack, React Next.js, sites qui convertissent">
    <meta name="author" content="VibeCoding - Thi<PERSON>ut Campana">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://vibecoding.ma">
    
    <!-- Open Graph / Social Media -->
    <meta property="og:title" content="ThibautDev | Développeur qui fait CONVERTIR | Expert IA Marrakech">
    <meta property="og:description" content="🎯 Sites web premium avec IA intégrée qui transforment visiteurs en clients. Expert Full-Stack basé à Marrakech. Client #1 Google 'golf marrakech'.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://thibautcampana.dev">
    <meta property="og:image" content="https://images.unsplash.com/photo-1677442136019-21780ecad995?w=1200&h=630&fit=crop">
    
    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="ThibautDev | Développeur Expert IA qui fait CONVERTIR">
    <meta name="twitter:description" content="🎯 Sites premium avec IA intégrée qui convertissent. Expert Full-Stack Marrakech. Client #1 Google 'golf marrakech'.">
    <meta name="twitter:image" content="https://images.unsplash.com/photo-1677442136019-21780ecad995?w=1200&h=630&fit=crop">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🚀</text></svg>">
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#f97316">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="ThibautDev">
    <link rel="apple-touch-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><circle cx='50' cy='50' r='50' fill='%23f97316'/><text x='50' y='70' font-size='60' text-anchor='middle' fill='white'>🚀</text></svg>">
    
    <!-- Fonts Optimisés -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        // Configuration Tailwind + suppression du warning de production
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'grotesk': ['Space Grotesk', 'sans-serif'],
                        'mono': ['JetBrains Mono', 'monospace']
                    },
                    colors: {
                        'primary': {
                            50: '#fff7ed',
                            100: '#ffedd5',
                            500: '#f97316',
                            600: '#ea580c',
                            700: '#dc2626',
                            900: '#7c2d12'
                        }
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'bounce-slow': 'bounce 3s infinite',
                        'fade-in-up': 'fadeInUp 0.6s ease-out',
                        'scale-in': 'scaleIn 0.5s ease-out'
                    },
                    keyframes: {
                        fadeInUp: {
                            '0%': { opacity: '0', transform: 'translateY(30px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        scaleIn: {
                            '0%': { opacity: '0', transform: 'scale(0.9)' },
                            '100%': { opacity: '1', transform: 'scale(1)' }
                        }
                    }
                }
            }
        };
        
        // Masquer le warning Tailwind en production
        console.warn = (function(originalWarn) {
            return function(message) {
                if (typeof message === 'string' && message.includes('cdn.tailwindcss.com should not be used in production')) {
                    return; // Ignorer ce warning spécifique
                }
                originalWarn.apply(console, arguments);
            };
        })(console.warn);
    </script>
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="css/styles.css">
    
    <!-- Analytics & Performance -->
    <script>
        // Performance monitoring
        if ('performance' in window) {
            window.addEventListener('load', () => {
                setTimeout(() => {
                    const perfData = performance.getEntriesByType('navigation')[0];
                    console.log(`🚀 Page loaded in ${Math.round(perfData.loadEventEnd - perfData.fetchStart)}ms`);
                }, 0);
            });
        }
    </script>
</head>
