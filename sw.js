/**
 * SERVICE WORKER - THIBAUT CAMPANA PORTFOLIO
 * Version: 2.0
 * Description: Service Worker optimisé avec stratégies de cache avancées
 */

const CACHE_VERSION = 'thibaut-portfolio-v2.0';
const STATIC_CACHE = `${CACHE_VERSION}-static`;
const DYNAMIC_CACHE = `${CACHE_VERSION}-dynamic`;
const IMAGE_CACHE = `${CACHE_VERSION}-images`;

// Ressources à mettre en cache immédiatement
const STATIC_ASSETS = [
    '/',
    '/index.html',
    '/css/styles.css',
    '/js/main.js',
    '/manifest.json',
    // Fonts critiques
    'https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap',
    // Icônes critiques
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css'
];

// URLs à mettre en cache dynamiquement
const DYNAMIC_URLS = [
    'https://fonts.gstatic.com/',
    'https://cdnjs.cloudflare.com/',
    'https://images.unsplash.com/'
];

// Durées de cache
const CACHE_DURATIONS = {
    static: 30 * 24 * 60 * 60 * 1000, // 30 jours
    dynamic: 7 * 24 * 60 * 60 * 1000, // 7 jours
    images: 30 * 24 * 60 * 60 * 1000   // 30 jours
};

// Configuration des stratégies de cache
const CACHE_STRATEGIES = {
    static: 'cache-first',
    dynamic: 'network-first',
    images: 'cache-first'
};

// ===== INSTALLATION =====
self.addEventListener('install', (event) => {
    console.log('🚀 Service Worker: Installation en cours...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then(cache => {
                console.log('📦 Service Worker: Mise en cache des ressources statiques');
                return cache.addAll(STATIC_ASSETS);
            })
            .then(() => {
                console.log('✅ Service Worker: Installé avec succès');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('❌ Service Worker: Erreur lors de l\'installation:', error);
            })
    );
});

// ===== ACTIVATION =====
self.addEventListener('activate', (event) => {
    console.log('🔄 Service Worker: Activation en cours...');
    
    event.waitUntil(
        Promise.all([
            // Nettoyer les anciens caches
            cleanupOldCaches(),
            // Prendre le contrôle immédiatement
            self.clients.claim()
        ]).then(() => {
            console.log('✅ Service Worker: Activé et contrôle pris');
            // Notifier les clients de la mise à jour
            notifyClients('Service Worker activé');
        })
    );
});

// ===== INTERCEPTION DES REQUETES =====
self.addEventListener('fetch', (event) => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Ignorer les requêtes non-HTTP
    if (!request.url.startsWith('http')) return;
    
    // Ignorer les requêtes de navigateur spécifiques
    if (request.url.includes('chrome-extension') || 
        request.url.includes('moz-extension') || 
        request.url.includes('safari-extension')) {
        return;
    }

    // Stratégie basée sur le type de ressource
    if (isStaticAsset(url)) {
        event.respondWith(handleStaticAsset(request));
    } else if (isImageRequest(url)) {
        event.respondWith(handleImageRequest(request));
    } else if (isDynamicContent(url)) {
        event.respondWith(handleDynamicContent(request));
    } else {
        event.respondWith(handleDefaultRequest(request));
    }
});

// ===== STRATEGIES DE CACHE =====

/**
 * Gestion des ressources statiques (CSS, JS, fonts)
 */
async function handleStaticAsset(request) {
    try {
        // Cache First Strategy
        const cachedResponse = await caches.match(request);
        if (cachedResponse && !isExpired(cachedResponse, CACHE_DURATIONS.static)) {
            return cachedResponse;
        }

        // Si pas en cache ou expiré, récupérer du réseau
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(STATIC_CACHE);
            await cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    } catch (error) {
        console.error('Erreur ressource statique:', error);
        // Retourner la version cache même si expirée
        return await caches.match(request);
    }
}

/**
 * Gestion des images
 */
async function handleImageRequest(request) {
    try {
        // Cache First Strategy pour les images
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }

        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(IMAGE_CACHE);
            await cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    } catch (error) {
        console.error('Erreur image:', error);
        // Image de fallback si disponible
        return await caches.match('/images/fallback.jpg') || 
               new Response('Image non disponible', { status: 404 });
    }
}

/**
 * Gestion du contenu dynamique
 */
async function handleDynamicContent(request) {
    try {
        // Network First Strategy
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            await cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    } catch (error) {
        console.error('Erreur contenu dynamique:', error);
        // Fallback vers le cache
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Page hors ligne personnalisée
        if (request.mode === 'navigate') {
            return await caches.match('/') || 
                   new Response('Page hors ligne', { 
                       status: 200, 
                       headers: { 'Content-Type': 'text/html' }
                   });
        }
        
        throw error;
    }
}

/**
 * Gestion par défaut
 */
async function handleDefaultRequest(request) {
    try {
        // Stale While Revalidate Strategy
        const cachedResponse = await caches.match(request);
        const networkResponsePromise = fetch(request).then(response => {
            if (response.ok) {
                const cache = caches.open(DYNAMIC_CACHE);
                cache.then(c => c.put(request, response.clone()));
            }
            return response;
        }).catch(() => cachedResponse);

        return cachedResponse || await networkResponsePromise;
    } catch (error) {
        console.error('Erreur requête par défaut:', error);
        throw error;
    }
}

// ===== FONCTIONS UTILITAIRES =====

/**
 * Vérifier si une ressource est statique
 */
function isStaticAsset(url) {
    const staticExtensions = ['.css', '.js', '.woff', '.woff2', '.ttf', '.otf'];
    const staticDomains = ['fonts.googleapis.com', 'fonts.gstatic.com', 'cdnjs.cloudflare.com'];
    
    return staticExtensions.some(ext => url.pathname.endsWith(ext)) ||
           staticDomains.some(domain => url.hostname.includes(domain));
}

/**
 * Vérifier si c'est une requête d'image
 */
function isImageRequest(url) {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.ico'];
    const imageDomains = ['images.unsplash.com', 'via.placeholder.com'];
    
    return imageExtensions.some(ext => url.pathname.toLowerCase().endsWith(ext)) ||
           imageDomains.some(domain => url.hostname.includes(domain)) ||
           url.pathname.includes('/images/');
}

/**
 * Vérifier si c'est du contenu dynamique
 */
function isDynamicContent(url) {
    const dynamicPaths = ['/api/', '/contact', '/form'];
    return dynamicPaths.some(path => url.pathname.includes(path));
}

/**
 * Vérifier si une réponse cache est expirée
 */
function isExpired(response, maxAge) {
    const dateHeader = response.headers.get('date');
    if (!dateHeader) return false;
    
    const responseDate = new Date(dateHeader);
    const now = new Date();
    return (now.getTime() - responseDate.getTime()) > maxAge;
}

/**
 * Nettoyer les anciens caches
 */
async function cleanupOldCaches() {
    const cacheNames = await caches.keys();
    const validCaches = [STATIC_CACHE, DYNAMIC_CACHE, IMAGE_CACHE];
    
    const deletionPromises = cacheNames
        .filter(cacheName => !validCaches.includes(cacheName))
        .map(cacheName => {
            console.log(`🗑️ Suppression ancien cache: ${cacheName}`);
            return caches.delete(cacheName);
        });
    
    return Promise.all(deletionPromises);
}

/**
 * Notifier les clients des mises à jour
 */
function notifyClients(message) {
    self.clients.matchAll().then(clients => {
        clients.forEach(client => {
            client.postMessage({
                type: 'UPDATE_AVAILABLE',
                message: message,
                version: CACHE_VERSION,
                timestamp: Date.now()
            });
        });
    });
}

// ===== GESTION DES MISES A JOUR =====
self.addEventListener('message', (event) => {
    const { data } = event;
    
    switch (data.type) {
        case 'SKIP_WAITING':
            self.skipWaiting();
            break;
            
        case 'GET_VERSION':
            event.ports[0].postMessage({
                version: CACHE_VERSION,
                caches: [STATIC_CACHE, DYNAMIC_CACHE, IMAGE_CACHE]
            });
            break;
            
        case 'CLEAR_CACHE':
            clearAllCaches().then(() => {
                event.ports[0].postMessage({ success: true });
            });
            break;
            
        case 'FORCE_UPDATE':
            // Forcer la mise à jour des ressources critiques
            updateCriticalResources().then(() => {
                event.ports[0].postMessage({ success: true });
            });
            break;
    }
});

/**
 * Vider tous les caches
 */
async function clearAllCaches() {
    const cacheNames = await caches.keys();
    return Promise.all(cacheNames.map(name => caches.delete(name)));
}

/**
 * Mettre à jour les ressources critiques
 */
async function updateCriticalResources() {
    const cache = await caches.open(STATIC_CACHE);
    return Promise.all(
        STATIC_ASSETS.map(async (url) => {
            try {
                const response = await fetch(url, { cache: 'reload' });
                if (response.ok) {
                    await cache.put(url, response);
                }
            } catch (error) {
                console.error(`Erreur mise à jour ${url}:`, error);
            }
        })
    );
}

// ===== GESTION DES ERREURS =====
self.addEventListener('error', (event) => {
    console.error('❌ Service Worker Error:', event.error);
});

self.addEventListener('unhandledrejection', (event) => {
    console.error('❌ Service Worker Unhandled Rejection:', event.reason);
    event.preventDefault();
});

// ===== NOTIFICATIONS PUSH (pour usage futur) =====
self.addEventListener('push', (event) => {
    if (!event.data) return;
    
    const options = {
        body: event.data.text(),
        icon: '/icons/icon-192x192.png',
        badge: '/icons/badge-72x72.png',
        vibrate: [200, 100, 200],
        data: {
            url: '/'
        },
        actions: [
            {
                action: 'open',
                title: 'Ouvrir le site'
            },
            {
                action: 'close',
                title: 'Fermer'
            }
        ]
    };
    
    event.waitUntil(
        self.registration.showNotification('Thibaut Dev Portfolio', options)
    );
});

self.addEventListener('notificationclick', (event) => {
    event.notification.close();
    
    if (event.action === 'open' || !event.action) {
        event.waitUntil(
            clients.openWindow(event.notification.data.url || '/')
        );
    }
});

console.log('🚀 Service Worker Thibaut Portfolio v2.0 chargé');
