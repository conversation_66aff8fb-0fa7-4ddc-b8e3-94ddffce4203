#!/bin/bash

# =======================================================================
# SCRIPT DE DÉPLOIEMENT - THIBAUT CAMPANA PORTFOLIO
# Version: 2.0
# Description: Script de déploiement automatisé avec optimisations
# Usage: ./deploy.sh [environment] [options]
# =======================================================================

set -e  # Arrêter en cas d'erreur

# Couleurs pour l'output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="Thibaut Campana Portfolio"
VERSION="2.0"
BUILD_DIR="dist"
BACKUP_DIR="backups"
LOG_FILE="deploy.log"

# Environnements
ENVIRONMENTS=("development" "staging" "production")
DEFAULT_ENV="production"

# URLs de déploiement
STAGING_URL="https://staging.thibautcampana.dev"
PRODUCTION_URL="https://thibautcampana.dev"

# =======================================================================
# FONCTIONS UTILITAIRES
# =======================================================================

# Affichage formaté
print_header() {
    echo -e "\n${PURPLE}=== $1 ===${NC}\n"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Logging
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
}

# Vérification des prérequis
check_prerequisites() {
    print_header "Vérification des prérequis"
    
    # Vérifier Git
    if ! command -v git &> /dev/null; then
        print_error "Git n'est pas installé"
        exit 1
    fi
    print_success "Git installé"
    
    # Vérifier Node.js (optionnel)
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_success "Node.js $NODE_VERSION installé"
    else
        print_warning "Node.js non installé (optionnel)"
    fi
    
    # Vérifier les outils de compression
    if command -v gzip &> /dev/null; then
        print_success "Gzip disponible"
    fi
    
    if command -v brotli &> /dev/null; then
        print_success "Brotli disponible"
    fi
    
    # Vérifier curl pour les tests
    if command -v curl &> /dev/null; then
        print_success "cURL disponible"
    fi
}

# Affichage de l'aide
show_help() {
    echo -e "${CYAN}$PROJECT_NAME - Script de Déploiement v$VERSION${NC}\n"
    echo "Usage: $0 [ENVIRONMENT] [OPTIONS]"
    echo ""
    echo "Environnements:"
    echo "  development    Déploiement local"
    echo "  staging        Déploiement staging"
    echo "  production     Déploiement production (défaut)"
    echo ""
    echo "Options:"
    echo "  -h, --help     Afficher cette aide"
    echo "  -v, --verbose  Mode verbeux"
    echo "  -b, --backup   Créer une sauvegarde avant déploiement"
    echo "  -t, --test     Exécuter les tests après déploiement"
    echo "  -o, --optimize Optimiser les ressources"
    echo "  --no-cache     Vider les caches"
    echo "  --dry-run      Simulation sans déploiement"
    echo ""
    echo "Exemples:"
    echo "  $0 production --backup --test"
    echo "  $0 staging --optimize"
    echo "  $0 --dry-run"
}

# Validation de l'environnement
validate_environment() {
    local env=$1
    
    if [[ ! " ${ENVIRONMENTS[@]} " =~ " ${env} " ]]; then
        print_error "Environnement invalide: $env"
        print_info "Environnements disponibles: ${ENVIRONMENTS[*]}"
        exit 1
    fi
}

# Création de sauvegarde
create_backup() {
    if [[ "$BACKUP" == "true" ]]; then
        print_header "Création de sauvegarde"
        
        local timestamp=$(date +"%Y%m%d_%H%M%S")
        local backup_name="${PROJECT_NAME}_${ENVIRONMENT}_${timestamp}"
        
        mkdir -p "$BACKUP_DIR"
        
        if [[ -d "$BUILD_DIR" ]]; then
            cp -r "$BUILD_DIR" "$BACKUP_DIR/$backup_name"
            print_success "Sauvegarde créée: $BACKUP_DIR/$backup_name"
        else
            print_warning "Aucun dossier de build à sauvegarder"
        fi
    fi
}

# Nettoyage des fichiers temporaires
cleanup() {
    print_header "Nettoyage"
    
    # Supprimer les fichiers temporaires
    find . -name "*.tmp" -delete 2>/dev/null || true
    find . -name ".DS_Store" -delete 2>/dev/null || true
    find . -name "Thumbs.db" -delete 2>/dev/null || true
    
    print_success "Fichiers temporaires supprimés"
}

# Optimisation des ressources
optimize_resources() {
    if [[ "$OPTIMIZE" == "true" ]]; then
        print_header "Optimisation des ressources"
        
        # Minification CSS
        if command -v cleancss &> /dev/null; then
            cleancss -o css/styles.min.css css/styles.css
            print_success "CSS minifié"
        else
            print_warning "cleancss non installé, minification CSS ignorée"
        fi
        
        # Minification JavaScript
        if command -v uglifyjs &> /dev/null; then
            uglifyjs js/main.js -o js/main.min.js -c -m
            print_success "JavaScript minifié"
        elif command -v terser &> /dev/null; then
            terser js/main.js -o js/main.min.js --compress --mangle
            print_success "JavaScript minifié avec Terser"
        else
            print_warning "Aucun minifieur JS installé"
        fi
        
        # Compression Gzip
        if command -v gzip &> /dev/null; then
            find . -type f \( -name "*.css" -o -name "*.js" -o -name "*.html" \) -exec gzip -k {} \;
            print_success "Fichiers compressés avec Gzip"
        fi
        
        # Compression Brotli
        if command -v brotli &> /dev/null; then
            find . -type f \( -name "*.css" -o -name "*.js" -o -name "*.html" \) -exec brotli -k {} \;
            print_success "Fichiers compressés avec Brotli"
        fi
        
        # Optimisation des images (si disponible)
        if command -v imagemin &> /dev/null; then
            print_info "Optimisation des images..."
            # imagemin images/* --out-dir=images/
            print_success "Images optimisées"
        fi
    fi
}

# Validation du code
validate_code() {
    print_header "Validation du code"
    
    # Validation HTML
    if command -v html5validator &> /dev/null; then
        html5validator --root . --show-warnings
        print_success "HTML validé"
    else
        print_warning "html5validator non installé"
    fi
    
    # Validation CSS
    if command -v csslint &> /dev/null; then
        csslint css/styles.css
        print_success "CSS validé"
    else
        print_warning "csslint non installé"
    fi
    
    # Validation JavaScript
    if command -v eslint &> /dev/null; then
        eslint js/main.js
        print_success "JavaScript validé"
    elif command -v jshint &> /dev/null; then
        jshint js/main.js
        print_success "JavaScript validé avec JSHint"
    else
        print_warning "Aucun linter JS installé"
    fi
}

# Tests de performance
performance_tests() {
    if [[ "$RUN_TESTS" == "true" ]]; then
        print_header "Tests de performance"
        
        local test_url
        case "$ENVIRONMENT" in
            "staging")
                test_url="$STAGING_URL"
                ;;
            "production")
                test_url="$PRODUCTION_URL"
                ;;
            *)
                test_url="http://localhost:8000"
                ;;
        esac
        
        # Test de base avec cURL
        if command -v curl &> /dev/null; then
            print_info "Test de connectivité: $test_url"
            
            local response_code=$(curl -s -o /dev/null -w "%{http_code}" "$test_url")
            if [[ "$response_code" == "200" ]]; then
                print_success "Site accessible (HTTP $response_code)"
            else
                print_error "Site non accessible (HTTP $response_code)"
            fi
            
            # Test des temps de réponse
            local response_time=$(curl -s -o /dev/null -w "%{time_total}" "$test_url")
            print_info "Temps de réponse: ${response_time}s"
        fi
        
        # Lighthouse (si disponible)
        if command -v lighthouse &> /dev/null; then
            print_info "Audit Lighthouse en cours..."
            lighthouse "$test_url" --output html --output-path lighthouse-report.html --quiet
            print_success "Rapport Lighthouse généré: lighthouse-report.html"
        else
            print_warning "Lighthouse non installé"
        fi
        
        # PageSpeed Insights (avec clé API)
        if [[ -n "$PAGESPEED_API_KEY" ]]; then
            print_info "Test PageSpeed Insights..."
            # Appel API PageSpeed
            # curl "https://www.googleapis.com/pagespeedonline/v5/runPagespeed?url=$test_url&key=$PAGESPEED_API_KEY"
            print_info "PageSpeed Insights - Voir console Google"
        fi
    fi
}

# Déploiement selon l'environnement
deploy() {
    if [[ "$DRY_RUN" == "true" ]]; then
        print_warning "Mode DRY RUN - Aucun déploiement effectué"
        return
    fi
    
    print_header "Déploiement en cours..."
    
    case "$ENVIRONMENT" in
        "development")
            deploy_local
            ;;
        "staging")
            deploy_staging
            ;;
        "production")
            deploy_production
            ;;
    esac
}

# Déploiement local
deploy_local() {
    print_info "Déploiement local"
    
    # Démarrer serveur local
    if command -v python3 &> /dev/null; then
        print_info "Démarrage serveur Python sur port 8000"
        python3 -m http.server 8000 &
        SERVER_PID=$!
        print_success "Serveur démarré (PID: $SERVER_PID)"
        print_info "Site accessible sur: http://localhost:8000"
    elif command -v node &> /dev/null && command -v npx &> /dev/null; then
        print_info "Démarrage serveur Node.js"
        npx serve . -p 8000 &
        SERVER_PID=$!
        print_success "Serveur démarré (PID: $SERVER_PID)"
    else
        print_warning "Aucun serveur local disponible"
    fi
}

# Déploiement staging
deploy_staging() {
    print_info "Déploiement vers staging"
    
    # Exemple avec rsync
    if [[ -n "$STAGING_SERVER" && -n "$STAGING_PATH" ]]; then
        rsync -avz --delete \
            --exclude '.git' \
            --exclude 'node_modules' \
            --exclude '*.log' \
            ./ "$STAGING_SERVER:$STAGING_PATH"
        print_success "Déploiement staging terminé"
    else
        print_warning "Configuration staging manquante"
    fi
}

# Déploiement production
deploy_production() {
    print_info "Déploiement vers production"
    
    # Confirmation pour production
    read -p "⚠️  Confirmer le déploiement en PRODUCTION ? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "Déploiement annulé"
        exit 0
    fi
    
    # Git push
    if git status --porcelain | grep -q .; then
        print_error "Des fichiers non commités existent"
        print_info "Commit et push requis avant déploiement production"
        exit 1
    fi
    
    git push origin main
    print_success "Code pushé vers Git"
    
    # Déploiement Netlify (si configuré)
    if command -v netlify &> /dev/null; then
        netlify deploy --prod --dir .
        print_success "Déploiement Netlify terminé"
    fi
    
    # Ou déploiement serveur personnalisé
    if [[ -n "$PRODUCTION_SERVER" && -n "$PRODUCTION_PATH" ]]; then
        rsync -avz --delete \
            --exclude '.git' \
            --exclude 'node_modules' \
            --exclude '*.log' \
            ./ "$PRODUCTION_SERVER:$PRODUCTION_PATH"
        print_success "Déploiement serveur terminé"
    fi
}

# Post-déploiement
post_deploy() {
    print_header "Post-déploiement"
    
    # Vider les caches
    if [[ "$CLEAR_CACHE" == "true" ]]; then
        print_info "Vidage des caches..."
        # Cloudflare API call si configuré
        if [[ -n "$CLOUDFLARE_ZONE_ID" && -n "$CLOUDFLARE_API_KEY" ]]; then
            curl -X POST "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/purge_cache" \
                -H "Authorization: Bearer $CLOUDFLARE_API_KEY" \
                -H "Content-Type: application/json" \
                --data '{"purge_everything":true}'
            print_success "Cache Cloudflare vidé"
        fi
    fi
    
    # Notification Slack/Discord (si configuré)
    if [[ -n "$SLACK_WEBHOOK" ]]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"🚀 Déploiement $PROJECT_NAME ($ENVIRONMENT) terminé avec succès\"}" \
            "$SLACK_WEBHOOK"
        print_success "Notification Slack envoyée"
    fi
    
    # Mise à jour sitemap
    if [[ "$ENVIRONMENT" == "production" ]]; then
        # Ping Google pour sitemap
        curl -s "https://www.google.com/ping?sitemap=https://thibautcampana.dev/sitemap.xml" > /dev/null
        print_success "Sitemap soumis à Google"
    fi
}

# Fonction principale
main() {
    # Banner
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════╗"
    echo "║           $PROJECT_NAME                     ║"
    echo "║              Script de Déploiement v$VERSION            ║"
    echo "║                                              ║"
    echo "║       Développé par Thibaut Campana         ║"
    echo "╚══════════════════════════════════════════════════╝"
    echo -e "${NC}\n"
    
    # Début du déploiement
    log "Début du déploiement - Environnement: $ENVIRONMENT"
    local start_time=$(date +%s)
    
    # Exécution des étapes
    check_prerequisites
    cleanup
    create_backup
    validate_code
    optimize_resources
    deploy
    performance_tests
    post_deploy
    
    # Fin du déploiement
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    print_header "Déploiement terminé"
    print_success "✅ Durée: ${duration}s"
    print_success "📝 Log: $LOG_FILE"
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        print_success "🌐 Site: $PRODUCTION_URL"
    elif [[ "$ENVIRONMENT" == "staging" ]]; then
        print_success "🌐 Site: $STAGING_URL"
    fi
    
    log "Déploiement terminé - Durée: ${duration}s"
}

# =======================================================================
# PARSING DES ARGUMENTS
# =======================================================================

ENVIRONMENT="$DEFAULT_ENV"
VERBOSE=false
BACKUP=false
RUN_TESTS=false
OPTIMIZE=false
CLEAR_CACHE=false
DRY_RUN=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -b|--backup)
            BACKUP=true
            shift
            ;;
        -t|--test)
            RUN_TESTS=true
            shift
            ;;
        -o|--optimize)
            OPTIMIZE=true
            shift
            ;;
        --no-cache)
            CLEAR_CACHE=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        development|staging|production)
            ENVIRONMENT=$1
            shift
            ;;
        *)
            print_error "Option inconnue: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validation de l'environnement
validate_environment "$ENVIRONMENT"

# Mode verbeux
if [[ "$VERBOSE" == "true" ]]; then
    set -x
fi

# Exécution principale
main

# Nettoyage à la sortie
trap cleanup EXIT