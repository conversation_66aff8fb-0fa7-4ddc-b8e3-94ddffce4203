{"name": "<PERSON><PERSON><PERSON><PERSON> - Développeur Full-Stack Expert", "short_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>, développeur Full-Stack expert et spécialiste IA basé à Marrakech", "start_url": "/", "display": "standalone", "background_color": "#0f172a", "theme_color": "#f97316", "orientation": "portrait-primary", "scope": "/", "lang": "fr-FR", "dir": "ltr", "categories": ["productivity", "business", "developer"], "screenshots": [{"src": "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=1280&h=720&fit=crop", "sizes": "1280x720", "type": "image/jpeg", "form_factor": "wide", "label": "Portfolio Thibaut Campana - Vue d'ensemble"}, {"src": "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=750&h=1334&fit=crop", "sizes": "750x1334", "type": "image/jpeg", "form_factor": "narrow", "label": "Portfolio Mobile - Développeur Expert"}], "icons": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 192 192'><circle cx='96' cy='96' r='96' fill='%23f97316'/><text x='96' y='140' font-size='100' text-anchor='middle' fill='white'>🚀</text></svg>", "sizes": "192x192", "type": "image/svg+xml", "purpose": "any"}, {"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'><circle cx='256' cy='256' r='256' fill='%23f97316'/><text x='256' y='350' font-size='280' text-anchor='middle' fill='white'>🚀</text></svg>", "sizes": "512x512", "type": "image/svg+xml", "purpose": "any"}, {"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 192 192'><circle cx='96' cy='96' r='96' fill='%23f97316'/><text x='96' y='140' font-size='100' text-anchor='middle' fill='white'>🚀</text></svg>", "sizes": "192x192", "type": "image/svg+xml", "purpose": "maskable"}], "shortcuts": [{"name": "Mes Services", "short_name": "Services", "description": "Découvrir mes services de développement", "url": "/#services", "icons": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 96 96'><circle cx='48' cy='48' r='48' fill='%23f97316'/><text x='48' y='65' font-size='50' text-anchor='middle' fill='white'>⚙️</text></svg>", "sizes": "96x96", "type": "image/svg+xml"}]}, {"name": "Portfolio", "short_name": "Projets", "description": "Voir mes réalisations", "url": "/#portfolio", "icons": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 96 96'><circle cx='48' cy='48' r='48' fill='%23f97316'/><text x='48' y='65' font-size='50' text-anchor='middle' fill='white'>💼</text></svg>", "sizes": "96x96", "type": "image/svg+xml"}]}, {"name": "Contact", "short_name": "<PERSON><PERSON>", "description": "Obtenir un devis gratuit", "url": "/#contact", "icons": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 96 96'><circle cx='48' cy='48' r='48' fill='%23f97316'/><text x='48' y='65' font-size='50' text-anchor='middle' fill='white'>📞</text></svg>", "sizes": "96x96", "type": "image/svg+xml"}]}], "related_applications": [{"platform": "webapp", "url": "https://thibautcampana.dev"}], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}}