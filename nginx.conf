# =======================================================================
# NGINX CONFIGURATION - THIBAUT CAMPANA PORTFOLIO
# Version: 2.0
# Description: Configuration Nginx haute performance
# =======================================================================

server {
    listen 80;
    listen [::]:80;
    server_name thibautcampana.dev www.thibautcampana.dev;
    
    # Redirection HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name thibautcampana.dev www.thibautcampana.dev;
    
    # Document root
    root /var/www/thibautcampana.dev;
    index index.html index.htm;
    
    # =======================================================================
    # SSL CONFIGURATION
    # =======================================================================
    
    ssl_certificate /etc/ssl/certs/thibautcampana.dev.crt;
    ssl_certificate_key /etc/ssl/private/thibautcampana.dev.key;
    
    # SSL optimisations
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 1d;
    ssl_session_tickets off;
    
    # OCSP Stapling
    ssl_stapling on;
    ssl_stapling_verify on;
    ssl_trusted_certificate /etc/ssl/certs/thibautcampana.dev.chain.crt;
    
    # =======================================================================
    # SECURITY HEADERS
    # =======================================================================
    
    # HSTS (HTTP Strict Transport Security)
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Permissions-Policy "camera=(), microphone=(), geolocation=(), payment=()" always;
    
    # Content Security Policy
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tailwindcss.com https://cdnjs.cloudflare.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' https: data:; connect-src 'self' https:; media-src 'self' https:; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'self';" always;
    
    # Remove server tokens
    server_tokens off;
    
    # =======================================================================
    # COMPRESSION
    # =======================================================================
    
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        application/atom+xml
        application/geo+json
        application/javascript
        application/x-javascript
        application/json
        application/ld+json
        application/manifest+json
        application/rdf+xml
        application/rss+xml
        application/xhtml+xml
        application/xml
        font/eot
        font/otf
        font/ttf
        image/svg+xml
        text/css
        text/javascript
        text/plain
        text/xml;
    
    # Brotli compression (si module disponible)
    # brotli on;
    # brotli_comp_level 6;
    # brotli_types text/xml image/svg+xml application/x-font-ttf image/vnd.microsoft.icon application/x-font-opentype application/json font/eot application/vnd.ms-fontobject application/javascript font/otf application/xml application/xhtml+xml text/javascript application/x-javascript text/plain application/xml+rss text/css;
    
    # =======================================================================
    # CACHING
    # =======================================================================
    
    # Assets statiques - Cache long
    location ~* \.(css|js|jpg|jpeg|png|gif|ico|svg|webp|avif|woff|woff2|ttf|otf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
        
        # CORS pour les fonts
        if ($request_filename ~* ^.*?\.(eot|woff|woff2|ttf|otf)$) {
            add_header Access-Control-Allow-Origin "*";
        }
    }
    
    # HTML - Cache court
    location ~* \.(html|htm)$ {
        expires 1h;
        add_header Cache-Control "public, must-revalidate";
    }
    
    # Service Worker - Pas de cache
    location = /sw.js {
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
    }
    
    # Manifest - Cache moyen
    location = /manifest.json {
        expires 1w;
        add_header Cache-Control "public";
    }
    
    # API endpoints - Pas de cache
    location /api/ {
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }
    
    # =======================================================================
    # ROUTES ET REDIRECTIONS
    # =======================================================================
    
    # Redirection www vers non-www
    if ($host = www.thibautcampana.dev) {
        return 301 https://thibautcampana.dev$request_uri;
    }
    
    # Single Page Application - Toutes les routes vers index.html
    location / {
        try_files $uri $uri/ /index.html;
        
        # Headers pour HTML
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
    }
    
    # =======================================================================
    # SÉCURITÉ FICHIERS
    # =======================================================================
    
    # Bloquer l'accès aux fichiers sensibles
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ \.(env|log|htaccess|htpasswd|ini|sh|sql|conf)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Bloquer les dossiers système
    location ~ ^/(\.git|node_modules|logs|temp|cache)/ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # =======================================================================
    # OPTIMISATIONS PERFORMANCE
    # =======================================================================
    
    # Buffers et timeouts
    client_max_body_size 50M;
    client_body_buffer_size 128k;
    client_header_buffer_size 3m;
    large_client_header_buffers 4 256k;
    
    # Timeouts
    client_body_timeout 60s;
    client_header_timeout 60s;
    keepalive_timeout 65s;
    send_timeout 60s;
    
    # Optimisations TCP
    tcp_nodelay on;
    tcp_nopush on;
    
    # =======================================================================
    # LOGS
    # =======================================================================
    
    # Logs d'accès personnalisés
    log_format portfolio '$remote_addr - $remote_user [$time_local] '
                        '"$request" $status $body_bytes_sent '
                        '"$http_referer" "$http_user_agent" '
                        '$request_time $upstream_response_time';
    
    access_log /var/log/nginx/thibautcampana.dev.access.log portfolio;
    error_log /var/log/nginx/thibautcampana.dev.error.log warn;
    
    # Ne pas logger les assets statiques
    location ~* \.(css|js|jpg|jpeg|png|gif|ico|svg|webp|woff|woff2|ttf|otf)$ {
        access_log off;
    }
    
    # =======================================================================
    # RATE LIMITING
    # =======================================================================
    
    # Limite pour le formulaire de contact
    location /contact {
        limit_req zone=contact burst=5 nodelay;
        try_files $uri $uri/ /index.html;
    }
    
    # Limite globale
    location / {
        limit_req zone=global burst=10 nodelay;
    }
    
    # =======================================================================
    # HEALTH CHECK
    # =======================================================================
    
    location /health {
        access_log off;
        return 200 "OK\n";
        add_header Content-Type text/plain;
    }
    
    # =======================================================================
    # PAGES D'ERREUR PERSONNALISÉES
    # =======================================================================
    
    error_page 404 /index.html;
    error_page 403 /index.html;
    error_page 500 502 503 504 /index.html;
}

# =======================================================================
# RATE LIMITING ZONES (à placer dans http context)
# =======================================================================

# Dans le contexte http {} du nginx.conf principal :
# limit_req_zone $binary_remote_addr zone=global:10m rate=10r/s;
# limit_req_zone $binary_remote_addr zone=contact:10m rate=1r/m;

# =======================================================================
# UPSTREAM POUR API (si nécessaire)
# =======================================================================

# upstream api_backend {
#     server 127.0.0.1:3000;
#     server 127.0.0.1:3001 backup;
#     keepalive 32;
# }

# server {
#     location /api/ {
#         proxy_pass http://api_backend;
#         proxy_http_version 1.1;
#         proxy_set_header Upgrade $http_upgrade;
#         proxy_set_header Connection 'upgrade';
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#         proxy_cache_bypass $http_upgrade;
#     }
# }

# =======================================================================
# MONITORING ET MÉTRIQUES
# =======================================================================

# Location pour métriques (nginx-prometheus-exporter)
# location /metrics {
#     allow 127.0.0.1;
#     deny all;
#     access_log off;
#     stub_status;
# }