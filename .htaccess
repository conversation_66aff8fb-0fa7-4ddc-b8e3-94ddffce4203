# =======================================================================
# THIBAUT CAMPANA PORTFOLIO - CONFIGURATION APACHE OPTIMISÉE
# Version: 2.0
# Description: Configuration haute performance avec sécurité renforcée
# =======================================================================

# Activer la réécriture d'URL
RewriteEngine On

# =======================================================================
# SÉCURITÉ
# =======================================================================

# Cacher la signature du serveur
ServerTokens Prod
Header always unset X-Powered-By
Header unset X-Powered-By

# Headers de sécurité
Header always set X-Content-Type-Options "nosniff"
Header always set X-Frame-Options "SAMEORIGIN"
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"
Header always set Permissions-Policy "camera=(), microphone=(), geolocation=()"

# Content Security Policy
Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tailwindcss.com https://cdnjs.cloudflare.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' https: data:; connect-src 'self' https:; media-src 'self' https:; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'self';"

# Empêcher l'accès aux fichiers sensibles
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak|env)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Empêcher l'accès aux dossiers système
RedirectMatch 403 ^/\.git
RedirectMatch 403 ^/\.env
RedirectMatch 403 ^/node_modules
RedirectMatch 403 ^/logs

# Protection contre les injections
<IfModule mod_rewrite.c>
    RewriteCond %{QUERY_STRING} (<|%3C).*script.*(>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} proc/self/environ [OR]
    RewriteCond %{QUERY_STRING} mosConfig_[a-zA-Z_]{1,21}(=|\%3D) [OR]
    RewriteCond %{QUERY_STRING} base64_(en|de)code\(.*\) [OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} javascript\: [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# =======================================================================
# COMPRESSION GZIP
# =======================================================================

<IfModule mod_deflate.c>
    # Activer la compression pour les types de fichiers
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
    AddOutputFilterByType DEFLATE application/ld+json
    AddOutputFilterByType DEFLATE image/svg+xml
    AddOutputFilterByType DEFLATE font/ttf
    AddOutputFilterByType DEFLATE font/otf
    AddOutputFilterByType DEFLATE font/woff
    AddOutputFilterByType DEFLATE font/woff2

    # Exclure les fichiers déjà compressés
    SetEnvIfNoCase Request_URI \
        \.(?:gif|jpe?g|png|zip|gz|rar|bz2|7z|mp3|mp4|avi|mov|wmv|flv)$ no-gzip dont-vary
</IfModule>

# =======================================================================
# MISE EN CACHE
# =======================================================================

<IfModule mod_expires.c>
    ExpiresActive On

    # HTML - Cache court pour permettre les mises à jour
    ExpiresByType text/html "access plus 1 hour"

    # CSS et JavaScript - Cache moyen
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"

    # Images - Cache long
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/x-icon "access plus 1 year"

    # Fonts - Cache très long
    ExpiresByType font/ttf "access plus 1 year"
    ExpiresByType font/otf "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"

    # Documents
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType application/json "access plus 1 hour"
    ExpiresByType application/xml "access plus 1 hour"
    ExpiresByType text/xml "access plus 1 hour"

    # Manifest et Service Worker - Cache court
    ExpiresByType application/manifest+json "access plus 1 week"
    ExpiresByType text/cache-manifest "access plus 0 seconds"
</IfModule>

# Headers de cache avec mod_headers
<IfModule mod_headers.c>
    # Cache pour les ressources statiques
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|webp|svg|ico|woff|woff2|ttf|otf)$">
        Header set Cache-Control "public, max-age=31536000, immutable"
    </FilesMatch>

    # Cache pour HTML
    <FilesMatch "\.(html|htm)$">
        Header set Cache-Control "public, max-age=3600, must-revalidate"
    </FilesMatch>

    # Service Worker - pas de cache
    <FilesMatch "sw\.js$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </FilesMatch>

    # Manifest - cache court
    <FilesMatch "manifest\.json$">
        Header set Cache-Control "public, max-age=604800"
    </FilesMatch>
</IfModule>

# =======================================================================
# OPTIMISATIONS DE PERFORMANCE
# =======================================================================

# ETags
<IfModule mod_headers.c>
    Header unset ETag
    FileETag None
</IfModule>

# Types MIME optimisés
<IfModule mod_mime.c>
    # JavaScript
    AddType application/javascript .js
    AddType application/json .json

    # Fonts
    AddType font/woff .woff
    AddType font/woff2 .woff2
    AddType font/ttf .ttf
    AddType font/otf .otf

    # Images modernes
    AddType image/webp .webp
    AddType image/avif .avif

    # Manifest
    AddType application/manifest+json .webmanifest
    AddType application/x-web-app-manifest+json .webapp
</IfModule>

# =======================================================================
# REDIRECTION ET RÉÉCRITURE D'URL
# =======================================================================

<IfModule mod_rewrite.c>
    # Forcer HTTPS (décommenter pour la production)
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

    # Redirection www vers non-www (décommenter si nécessaire)
    # RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
    # RewriteRule ^(.*)$ https://%1%{REQUEST_URI} [R=301,L]

    # Gestion des URLs propres pour SPA
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} !^/api/
    RewriteRule ^(.*)$ index.html [L]

    # Redirection des anciennes URLs (exemples)
    # Redirect 301 /old-page /new-page
</IfModule>

# =======================================================================
# OPTIMISATIONS SPÉCIFIQUES
# =======================================================================

# Pré-chargement des ressources critiques
<IfModule mod_headers.c>
    # Preload des fonts critiques
    Header add Link "</css/styles.css>; rel=preload; as=style"
    Header add Link "</js/main.js>; rel=preload; as=script"
    
    # Préconnexion aux domaines externes
    Header add Link "<https://fonts.googleapis.com>; rel=preconnect"
    Header add Link "<https://fonts.gstatic.com>; rel=preconnect; crossorigin"
    Header add Link "<https://cdnjs.cloudflare.com>; rel=preconnect"
</IfModule>

# Désactiver l'affichage des répertoires
Options -Indexes

# Pages d'erreur personnalisées
ErrorDocument 404 /index.html
ErrorDocument 403 /index.html
ErrorDocument 500 /index.html

# =======================================================================
# CONFIGURATION POUR PWA
# =======================================================================

# Service Worker headers
<FilesMatch "sw\.js$">
    Header set Service-Worker-Allowed "/"
</FilesMatch>

# Headers PWA
<FilesMatch "manifest\.json$">
    Header set Access-Control-Allow-Origin "*"
</FilesMatch>

# =======================================================================
# MONITORING ET LOGS
# =======================================================================

# Format de log personnalisé (si LogFormat est supporté)
# LogFormat "%h %l %u %t \"%r\" %>s %O \"%{Referer}i\" \"%{User-Agent}i\" %D" portfolio
# CustomLog logs/portfolio_access.log portfolio

# =======================================================================
# VARIABLES D'ENVIRONNEMENT
# =======================================================================

# Définir des variables pour l'application
SetEnv ENVIRONMENT production
SetEnv APP_NAME "Thibaut Campana Portfolio"
SetEnv APP_VERSION "2.0"

# =======================================================================
# FIN DE CONFIGURATION
# =======================================================================