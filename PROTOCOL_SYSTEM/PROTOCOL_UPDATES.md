# 🔄 PROTOCOL UPDATES - BEST PRACTICES ÉVOLUTIVES

**VERSION:** 1.0 | **CRÉÉ:** 2025-07-16

## 🎯 OBJECTIF DU PROTOCOLE

Éliminer DÉFINITIVEMENT :
- ❌ Hallucinations Claude sur état du projet
- ❌ Oublis entre sessions
- ❌ Répétitions d'informations
- ❌ Confusion sur ce qui est fait/à faire
- ❌ Perte de contexte business

## 📋 CHECKLIST DÉBUT SESSION

Avant TOUTE réponse, Claude DOIT :
1. **Lire SESSION_STATE.md** - État actuel exact
2. **Consulter MEMORY_ANCHORS.md** - Points fixes
3. **Vérifier PROGRESS_TRACKER.md** - Avancement réel
4. **Mettre à jour si nécessaire** - Après chaque action

## 🔧 RÈGLES D'OR

### ✅ TOUJOURS FAIRE
- Vérifier fichiers AVANT de supposer quoi que ce soit
- Donner solutions copy-paste directes
- Maintenir focus sur exécution rapide
- Mettre à jour progress après chaque action
- Rester dans mindset "partner stratégique"

### 🚫 NE JAMAIS FAIRE
- Dire "il manque des documents" sans avoir vérifié
- Créer confusion sur état du projet
- Proposer de "recréer" ce qui existe déjà
- Oublier le contexte Marrakech/IA/Premium
- Perdre de vue l'objectif 30K MAD/mois

## 🔄 MISE À JOUR AUTOMATIQUE

Chaque fois qu'une action est complétée :
1. **SESSION_STATE.md** → Mettre à jour section "EN COURS"
2. **PROGRESS_TRACKER.md** → Cocher les tâches finies
3. **PROTOCOL_UPDATES.md** → Ajouter best practice si besoin

## 📚 APPRENTISSAGE CONTINU

### Version 1.1 - Améliorations identifiées
- [À compléter au fur et à mesure]

### Version 1.2 - Optimisations futures
- [À compléter selon feedback Thibaut]

## 🚀 ACTIVATION PROTOCOLE

**PHRASE TRIGGER:** "CONTINUE" ou référence au dossier
**ACTION:** Lecture automatique des 4 fichiers protocol
**RÉSULTAT:** Reprise exacte là où on s'est arrêté

---
*Ce protocole évolue avec notre collaboration pour devenir de plus en plus efficace*