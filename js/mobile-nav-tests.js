/**
 * 🧪 TESTS AUTOMATISÉS MENU MOBILE - SILICON VALLEY EDITION
 * Suite de tests pour valider le fonctionnement du menu hamburger
 */

class MobileNavTester {
    constructor() {
        this.tests = [];
        this.results = {
            passed: 0,
            failed: 0,
            total: 0
        };
        this.init();
    }

    init() {
        // Attendre que le DOM soit chargé
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.runTests());
        } else {
            this.runTests();
        }
    }

    async runTests() {
        console.log('🧪 Démarrage des tests menu mobile...');
        
        // Tests de base
        this.addTest('Elements DOM présents', this.testDOMElements);
        this.addTest('Classes CSS appliquées', this.testCSSClasses);
        this.addTest('Event listeners attachés', this.testEventListeners);
        this.addTest('État initial correct', this.testInitialState);
        
        // Tests fonctionnels
        this.addTest('Ouverture du menu', this.testMenuOpen);
        this.addTest('Fermeture du menu', this.testMenuClose);
        this.addTest('Navigation par liens', this.testLinkNavigation);
        this.addTest('Gestion du scroll', this.testScrollLock);
        
        // Tests d'accessibilité
        this.addTest('Attributs ARIA', this.testARIAAttributes);
        this.addTest('Navigation clavier', this.testKeyboardNavigation);
        
        // Tests de performance
        this.addTest('Temps de réponse', this.testPerformance);
        
        // Exécuter tous les tests
        await this.executeTests();
        this.displayResults();
    }

    addTest(name, testFunction) {
        this.tests.push({ name, test: testFunction.bind(this) });
    }

    async executeTests() {
        for (const { name, test } of this.tests) {
            try {
                const result = await test();
                this.logResult(name, result, null);
                if (result) this.results.passed++;
                else this.results.failed++;
            } catch (error) {
                this.logResult(name, false, error);
                this.results.failed++;
            }
            this.results.total++;
        }
    }

    // Tests individuels
    testDOMElements() {
        const toggle = document.querySelector('#mobile-menu-toggle');
        const menu = document.querySelector('#mobile-menu');
        const overlay = document.querySelector('#mobile-menu-overlay');
        
        return !!(toggle && menu && overlay);
    }

    testCSSClasses() {
        const menu = document.querySelector('#mobile-menu');
        return menu && menu.classList.contains('mobile-menu-hidden');
    }

    testEventListeners() {
        const toggle = document.querySelector('#mobile-menu-toggle');
        // Vérifier si des event listeners sont attachés (approximatif)
        return toggle && toggle.onclick !== null || toggle.addEventListener;
    }

    testInitialState() {
        const toggle = document.querySelector('#mobile-menu-toggle');
        const menu = document.querySelector('#mobile-menu');
        
        return toggle && 
               toggle.getAttribute('aria-expanded') === 'false' &&
               menu &&
               menu.classList.contains('mobile-menu-hidden');
    }

    async testMenuOpen() {
        const toggle = document.querySelector('#mobile-menu-toggle');
        const menu = document.querySelector('#mobile-menu');
        
        if (!toggle || !menu) return false;
        
        // Simuler un clic
        toggle.click();
        
        // Attendre l'animation
        await this.wait(400);
        
        return menu.classList.contains('mobile-menu-visible') &&
               toggle.getAttribute('aria-expanded') === 'true';
    }

    async testMenuClose() {
        const toggle = document.querySelector('#mobile-menu-toggle');
        const menu = document.querySelector('#mobile-menu');
        
        if (!toggle || !menu) return false;
        
        // S'assurer que le menu est ouvert
        if (!menu.classList.contains('mobile-menu-visible')) {
            toggle.click();
            await this.wait(400);
        }
        
        // Fermer le menu
        toggle.click();
        await this.wait(400);
        
        return menu.classList.contains('mobile-menu-hidden') &&
               toggle.getAttribute('aria-expanded') === 'false';
    }

    async testLinkNavigation() {
        const menu = document.querySelector('#mobile-menu');
        const links = menu ? menu.querySelectorAll('a[href^="#"]') : [];
        
        return links.length > 0;
    }

    testScrollLock() {
        // Tester si les classes de scroll lock sont disponibles
        const hasScrollLockCSS = Array.from(document.styleSheets).some(sheet => {
            try {
                return Array.from(sheet.cssRules).some(rule => 
                    rule.selectorText && rule.selectorText.includes('menu-scroll-locked')
                );
            } catch (e) {
                return false;
            }
        });
        
        return hasScrollLockCSS;
    }

    testARIAAttributes() {
        const toggle = document.querySelector('#mobile-menu-toggle');
        const menu = document.querySelector('#mobile-menu');
        
        return toggle && 
               toggle.hasAttribute('aria-expanded') &&
               toggle.hasAttribute('aria-label') &&
               menu &&
               menu.hasAttribute('role');
    }

    testKeyboardNavigation() {
        const toggle = document.querySelector('#mobile-menu-toggle');
        return toggle && toggle.tabIndex >= 0;
    }

    async testPerformance() {
        const start = performance.now();
        
        const toggle = document.querySelector('#mobile-menu-toggle');
        if (!toggle) return false;
        
        // Test d'ouverture/fermeture rapide
        toggle.click();
        await this.wait(100);
        toggle.click();
        
        const end = performance.now();
        const duration = end - start;
        
        // Le test doit prendre moins de 1 seconde
        return duration < 1000;
    }

    // Utilitaires
    wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    logResult(testName, passed, error) {
        const icon = passed ? '✅' : '❌';
        const message = `${icon} ${testName}`;
        
        if (passed) {
            console.log(`%c${message}`, 'color: #10b981;');
        } else {
            console.log(`%c${message}`, 'color: #ef4444;');
            if (error) console.error('Erreur:', error);
        }
    }

    displayResults() {
        const { passed, failed, total } = this.results;
        const percentage = Math.round((passed / total) * 100);
        
        console.log('\n🏆 RÉSULTATS DES TESTS MENU MOBILE');
        console.log('=====================================');
        console.log(`✅ Tests réussis: ${passed}/${total}`);
        console.log(`❌ Tests échoués: ${failed}/${total}`);
        console.log(`📊 Taux de réussite: ${percentage}%`);
        
        if (percentage >= 90) {
            console.log('🚀 EXCELLENT! Menu mobile niveau Silicon Valley!');
        } else if (percentage >= 75) {
            console.log('👍 BIEN! Quelques améliorations possibles.');
        } else {
            console.log('⚠️ ATTENTION! Des corrections sont nécessaires.');
        }
        
        // Exposer les résultats globalement pour debug
        window.mobileNavTestResults = this.results;
    }
}

// Auto-démarrage des tests en mode debug
if (window.MOBILE_NAV_CONFIG && window.MOBILE_NAV_CONFIG.DEBUG.ENABLED) {
    new MobileNavTester();
}

// Export pour utilisation manuelle
window.MobileNavTester = MobileNavTester;
