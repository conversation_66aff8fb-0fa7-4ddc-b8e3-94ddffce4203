/**
 * 💻 TERMINAL ANIMATION SILICON VALLEY EDITION
 * Animation du terminal geek pour le profil de Thibaut
 */

class TerminalAnimation {
    constructor() {
        this.terminalContent = null;
        this.codeText = `// Thibaut Campana - Expert Full-Stack & IA 🚀
// Ba<PERSON><PERSON> Marrakech, Maroc 🇲🇦

const developer = {
  name: 'Thibaut Campana',
  location: 'Marrakech, Morocco 🇲🇦',
  specialties: [
    'Intelligence Artificielle Avancée',
    'Développement Full-Stack Premium',
    'SEO & Growth Hacking',
    'Solutions Business sur-mesure'
  ],
  experience: '7+ années',
  projets: 25,
  satisfaction: '100%',
  mission: 'Transformer vos idées en succès digital ✨'
};

function createMagic(yourIdea) {
  const result = developer.analyze(yourIdea)
    .design() 
    .develop()
    .optimize()
    .deploy();
  
  return result + ' 🚀 BOOM! Votre projet est live!';
}

function getDevisGratuit() {
  return {
    délai: '24h maximum',
    garantie: 'Satisfait ou remboursé',
    support: '7j/7',
    message: 'Parlons de votre projet dès maintenant! 💬'
  };
}

// Ready to transform your business? 
console.log('🔥 Thibaut is ready for your next big project!');`;
        
        this.index = 0;
        this.currentText = '';
        this.isTyping = false;
        
        this.init();
    }

    init() {
        // Attendre que le DOM soit chargé
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupTerminal());
        } else {
            this.setupTerminal();
        }
    }

    setupTerminal() {
        this.terminalContent = document.getElementById('terminal-content');
        
        if (!this.terminalContent) {
            console.warn('⚠️ Terminal content non trouvé');
            return;
        }

        // Observer pour démarrer l'animation quand le terminal est visible
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && !this.isTyping) {
                    this.startTyping();
                    observer.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.3
        });

        const terminal = document.querySelector('.terminal');
        if (terminal) {
            observer.observe(terminal);
        } else {
            // Fallback si pas d'intersection observer
            setTimeout(() => this.startTyping(), 2000);
        }

        console.log('💻 Terminal animation initialisée');
    }

    startTyping() {
        if (this.isTyping) return;
        
        this.isTyping = true;
        this.index = 0;
        this.currentText = '';
        
        // Initialiser le terminal avec juste le curseur
        this.terminalContent.innerHTML = '<span class="typing-cursor">_</span>';
        
        console.log('⌨️ Démarrage de l\'animation terminal...');
        
        // Démarrer l'animation après un petit délai
        setTimeout(() => this.typeChar(), 1000);
    }

    typeChar() {
        if (this.index < this.codeText.length) {
            this.currentText += this.codeText[this.index];
            
            // Formatage de la syntaxe avec les couleurs premium
            let formattedText = this.formatCode(this.currentText);
            
            this.terminalContent.innerHTML = formattedText + '<span class="typing-cursor">_</span>';
            
            this.index++;
            
            // Vitesse d'écriture variable pour plus de réalisme
            const delay = this.getTypingDelay(this.codeText[this.index - 1]);
            setTimeout(() => this.typeChar(), delay);
            
        } else {
            // Animation terminée
            this.finishTyping();
        }
    }

    formatCode(text) {
        return text
            // Commentaires en gris
            .replace(/\/\/.*$/gm, '<span class="code-comment">$&</span>')
            // Mots-clés en orange
            .replace(/\b(const|function|return|console|log)\b/g, '<span class="code-keyword">$1</span>')
            // Variables en orange foncé
            .replace(/\b(developer|name|location|specialties|experience|projets|satisfaction|mission|result|yourIdea|délai|garantie|support|message)\b/g, '<span class="code-variable">$1</span>')
            // Fonctions en bleu
            .replace(/\b(analyze|design|develop|optimize|deploy|createMagic|getDevisGratuit)\b/g, '<span class="code-function">$1</span>')
            // Chaînes en vert
            .replace(/'([^']*)'/g, '<span class="code-string">\'$1\'</span>')
            // Nombres en violet
            .replace(/\b(\d+\+?)\b/g, '<span class="code-number">$1</span>')
            // Saut de ligne
            .replace(/\n/g, '<br>');
    }

    getTypingDelay(char) {
        // Vitesse d'écriture variable pour plus de réalisme
        if (char === '\n') return 150;
        if (char === '.') return 200;
        if (char === ' ') return 30;
        if (char === '{' || char === '}') return 100;
        return Math.random() * 50 + 25; // Entre 25 et 75ms
    }

    finishTyping() {
        console.log('✅ Animation terminal terminée');
        
        // Faire clignoter le curseur
        setTimeout(() => {
            const cursor = this.terminalContent.querySelector('.typing-cursor');
            if (cursor) {
                cursor.style.animation = 'blink 1s infinite';
            }
        }, 500);

        this.isTyping = false;
    }

    // API publique pour redémarrer l'animation
    restart() {
        if (!this.isTyping) {
            this.startTyping();
        }
    }

    // API publique pour arrêter l'animation
    stop() {
        this.isTyping = false;
        if (this.terminalContent) {
            this.terminalContent.innerHTML = '<span class="typing-cursor">_</span>';
        }
    }
}

// Auto-initialisation
const terminalAnimation = new TerminalAnimation();

// Exposer globalement pour debug
window.terminalAnimation = terminalAnimation;

// Export pour utilisation externe
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TerminalAnimation;
}

console.log('💻 Terminal Animation Silicon Valley chargé !');
