// ======================================
// THIBAUT CAMPANA - PORTFOLIO ELITE JS
// Animation Terminal Premium
// ======================================

document.addEventListener('DOMContentLoaded', function() {
    
    // Animation du terminal de code
    function setupTerminal() {
        const terminalContent = document.getElementById('terminal-content');
        if (!terminalContent) return;

        const codeText = `// Thibaut Campana - Expert Full-Stack & IA 🚀
// Basé à Marrakech, Maroc 🇲🇦

const developer = {
  name: 'Thibaut Campana',
  location: 'Marrakech, Morocco 🇲🇦',
  specialties: [
    'Intelligence Artificielle Avancée',
    'Développement Full-Stack Premium',
    'SEO & Growth Hacking',
    'Solutions Business sur-mesure'
  ],
  experience: '7+ années',
  projets: 25,
  satisfaction: '100%',
  mission: 'Transformer vos idées en succès digital ✨'
};

function createMagic(yourIdea) {
  const result = developer.analyze(yourIdea)
    .design() 
    .develop()
    .optimize()
    .deploy();
  
  return result + ' 🚀 BOOM! Votre projet est live!';
}

function getDevisGratuit() {
  return {
    délai: '24h maximum',
    garantie: 'Satisfait ou remboursé',
    support: '7j/7',
    message: 'Parlons de votre projet dès maintenant! 💬'
  };
}

// Ready to transform your business? 
console.log('🔥 Thibaut is ready for your next big project!');`;

        let index = 0;
        let currentText = '';
        
        terminalContent.innerHTML = '<span class="typing-cursor">_</span>';
        
        const typeChar = () => {
            if (index < codeText.length) {
                currentText += codeText[index];
                
                // Formatage de la syntaxe avec les couleurs premium
                let formattedText = currentText
                    // Commentaires en gris
                    .replace(/\/\/.*$/gm, '<span class="code-comment">$&</span>')
                    // Mots-clés en orange
                    .replace(/\b(const|function|return|console|log)\b/g, '<span class="code-keyword">$1</span>')
                    // Variables en orange foncé
                    .replace(/\b(developer|name|location|specialties|experience|projets|satisfaction|mission|result|yourIdea|délai|garantie|support|message)\b/g, '<span class="code-variable">$1</span>')
                    // Fonctions en bleu
                    .replace(/\b(analyze|design|develop|optimize|deploy|createMagic|getDevisGratuit)\b/g, '<span class="code-function">$1</span>')
                    // Chaînes en vert
                    .replace(/'([^']*)'/g, '<span class="code-string">\'$1\'</span>')
                    // Nombres en violet
                    .replace(/\b(\d+\+?)\b/g, '<span class="code-number">$1</span>')
                    // Saut de ligne
                    .replace(/\n/g, '<br>');
                
                terminalContent.innerHTML = formattedText + '<span class="typing-cursor">_</span>';
                
                index++;
                // Vitesse d'écriture variable pour plus de réalisme
                const delay = codeText[index-1] === '\n' ? 150 : 
                             codeText[index-1] === '.' ? 200 :
                             codeText[index-1] === ' ' ? 30 : 50;
                setTimeout(typeChar, delay);
            } else {
                // Animation terminée, faire clignoter le curseur
                setTimeout(() => {
                    const cursor = terminalContent.querySelector('.typing-cursor');
                    if (cursor) {
                        cursor.style.animation = 'blink 1s infinite';
                    }
                }, 500);
            }
        };
        
        // Démarrer l'animation après 1 seconde
        setTimeout(typeChar, 1000);
    }

    // Portfolio filters animation
    function setupPortfolioFilters() {
        const filters = document.querySelectorAll('.portfolio-filter');
        const items = document.querySelectorAll('.portfolio-item');

        filters.forEach(filter => {
            filter.addEventListener('click', () => {
                // Retirer la classe active de tous les filtres
                filters.forEach(f => f.classList.remove('active'));
                filter.classList.add('active');

                const filterValue = filter.getAttribute('data-filter');

                items.forEach(item => {
                    if (filterValue === 'all' || item.getAttribute('data-category') === filterValue) {
                        item.style.display = 'block';
                        item.style.animation = 'fadeInUp 0.6s ease-out';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });
    }

    // Smooth scrolling pour les liens de navigation
    function setupSmoothScrolling() {
        const navLinks = document.querySelectorAll('a[href^="#"]');
        
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    // Animation des statistiques - SÉCURISÉE
    function animateStats() {
        // Sélectionner seulement les stats qui peuvent être animées en sécurité
        const animatableStats = document.querySelectorAll('[data-animate-stat]');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const target = entry.target;
                    const finalValue = target.getAttribute('data-final-value');
                    const startValue = target.getAttribute('data-start-value') || '0';
                    
                    if (finalValue && !target.hasAttribute('data-animated')) {
                        target.setAttribute('data-animated', 'true');
                        animateNumber(target, parseInt(startValue), parseInt(finalValue), 2000);
                    }
                }
            });
        });
        
        animatableStats.forEach(stat => observer.observe(stat));
    }

    function animateNumber(element, start, end, duration) {
        const range = end - start;
        if (range === 0) return;
        
        const increment = end > start ? 1 : -1;
        const stepTime = Math.abs(Math.floor(duration / range));
        let current = start;
        
        const originalText = element.textContent;
        const hasPlus = originalText.includes('+');
        const hasPercent = originalText.includes('%');
        
        const timer = setInterval(() => {
            current += increment;
            let displayText = current.toString();
            
            // Ajouter le préfixe "+" si présent dans l'original
            if (hasPlus && current > 0) {
                displayText = '+' + displayText;
            }
            
            // Ajouter le suffixe "%" si présent dans l'original  
            if (hasPercent) {
                displayText = displayText + '%';
            }
            
            element.textContent = displayText;
            
            if (current === end) {
                clearInterval(timer);
            }
        }, stepTime);
    }

    // Menu mobile SIMPLIFIÉ et ROBUSTE
    function setupMobileMenu() {
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        const mobileMenu = document.getElementById('mobile-menu');
        
        console.log('📱 Setup mobile menu - Elements found:', {
            toggle: !!mobileMenuToggle,
            menu: !!mobileMenu
        });
        
        if (!mobileMenuToggle || !mobileMenu) {
            console.error('❌ Menu mobile elements missing!');
            return;
        }

        let isMenuOpen = false;

        // Fonction pour ouvrir le menu
        function openMenu() {
            mobileMenu.classList.remove('hidden');
            mobileMenuToggle.innerHTML = '<i class="fas fa-times text-xl"></i>';
            isMenuOpen = true;
            console.log('📱 Menu OUVERT');
        }

        // Fonction pour fermer le menu
        function closeMenu() {
            mobileMenu.classList.add('hidden');
            mobileMenuToggle.innerHTML = '<i class="fas fa-bars text-xl"></i>';
            isMenuOpen = false;
            console.log('📱 Menu FERMÉ');
        }

        // Toggle du menu au clic du bouton
        mobileMenuToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            console.log('📱 Button clicked - Menu state:', isMenuOpen);
            
            if (isMenuOpen) {
                closeMenu();
            } else {
                openMenu();
            }
        });

        // Fermer le menu sur clic des liens
        const menuLinks = mobileMenu.querySelectorAll('a');
        menuLinks.forEach(link => {
            link.addEventListener('click', function() {
                closeMenu();
                console.log('📱 Menu fermé par clic sur lien');
            });
        });

        // Fermer le menu si clic en dehors
        document.addEventListener('click', function(e) {
            if (isMenuOpen && 
                !mobileMenu.contains(e.target) && 
                !mobileMenuToggle.contains(e.target)) {
                closeMenu();
                console.log('📱 Menu fermé par clic extérieur');
            }
        });

        // S'assurer que le menu est fermé au démarrage
        closeMenu();
    }

    // Initialisation de tous les effets
    setupTerminal();
    setupPortfolioFilters(); 
    setupSmoothScrolling();
    animateStats();
    setupMobileMenu();
    setupFormModal(); // Nouvelle fonction pour la modale

    // Effet de parallaxe subtil sur les éléments flottants
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll('.floating-orb, .animate-float');
        
        parallaxElements.forEach(element => {
            const speed = 0.5;
            element.style.transform = `translateY(${scrolled * speed}px)`;
        });
    });

    console.log('🚀 Portfolio Elite loaded successfully!');
    console.log('💬 Contact: <EMAIL>');
    console.log('📱 WhatsApp: +212 630 728 089');
});

// ======================================
// MODALE DE CONFIRMATION PREMIUM
// Conforme aux spécifications UX/Accessibility
// ======================================

function setupFormModal() {
    const form = document.getElementById('contact-form');
    const modal = document.getElementById('success-modal');
    const modalCloseBtn = document.getElementById('modal-close-btn');
    const submitBtn = form?.querySelector('button[type="submit"]');
    
    if (!form || !modal || !modalCloseBtn || !submitBtn) {
        console.error('❌ Éléments de modale manquants');
        return;
    }

    console.log('✅ Configuration modale de confirmation');

    // Classes et messages configurables
    const CONFIG = {
        loadingClass: 'btn-loading',
        successClass: 'form-success',
        errorClass: 'form-error',
        messages: {
            loading: 'Envoi en cours...',
            success: 'C\'est dans la boîte ! 🚀',
            error: 'Oups, une erreur est survenue. Veuillez réessayer.'
        }
    };

    // État du formulaire
    let isSubmitting = false;
    let focusableElements = [];
    let firstFocusableElement = null;
    let lastFocusableElement = null;

    // Gestion de la soumission du formulaire
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        if (isSubmitting) return;
        
        console.log('📧 Soumission du formulaire...');
        
        // Validation basique
        const formData = new FormData(form);
        const name = formData.get('name')?.toString().trim();
        const email = formData.get('email')?.toString().trim();
        const message = formData.get('message')?.toString().trim();
        
        if (!name || !email || !message) {
            showError('Veuillez remplir tous les champs obligatoires.');
            return;
        }
        
        // Démarrer l'état de chargement
        setLoadingState(true);
        
        try {
            // Utiliser la fonction d'API configurée
            const result = await sendFormData(formData);
            
            if (result.success) {
                console.log('✅ Formulaire envoyé avec succès');
                showSuccessModal();
                resetForm();
            } else {
                throw new Error(result.message || 'Échec de la soumission');
            }
        } catch (error) {
            console.error('❌ Erreur lors de l\'envoi:', error);
            showError(error.message || CONFIG.messages.error);
        } finally {
            setLoadingState(false);
        }
    });

    // Gestion de l'état de chargement
    function setLoadingState(loading) {
        isSubmitting = loading;
        
        if (loading) {
            submitBtn.disabled = true;
            submitBtn.classList.add(CONFIG.loadingClass);
            submitBtn.textContent = CONFIG.messages.loading;
        } else {
            submitBtn.disabled = false;
            submitBtn.classList.remove(CONFIG.loadingClass);
            submitBtn.innerHTML = '🚀 Envoyer ma Demande de Devis<i class="fas fa-paper-plane ml-2 group-hover:translate-x-1 transition-transform"></i>';
        }
    }

    // Afficher la modale de succès
    function showSuccessModal() {
        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
        
        // Setup du focus trap
        setupFocusTrap();
        
        // Focus sur le bouton de fermeture
        setTimeout(() => {
            modalCloseBtn.focus();
        }, 300);
        
        console.log('🎉 Modale de succès affichée');
    }

    // Fermer la modale
    function closeModal() {
        modal.classList.add('hidden');
        document.body.style.overflow = '';
        
        // Retour du focus au formulaire
        setTimeout(() => {
            const firstInput = form.querySelector('input, textarea');
            firstInput?.focus();
        }, 100);
        
        console.log('❌ Modale fermée');
    }

    // Afficher une erreur
    function showError(message) {
        removeAllStates();
        
        let errorDiv = form.querySelector('.error-message');
        if (!errorDiv) {
            errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            submitBtn.parentNode.appendChild(errorDiv);
        }
        
        errorDiv.textContent = message;
        errorDiv.classList.add('show');
        
        // Auto-hide après 5 secondes
        setTimeout(() => {
            errorDiv.classList.remove('show');
        }, 5000);
    }

    // Réinitialiser le formulaire
    function resetForm() {
        form.reset();
        removeAllStates();
    }

    // Supprimer tous les états visuels
    function removeAllStates() {
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.classList.remove(CONFIG.successClass, CONFIG.errorClass);
        });
        
        const errorDiv = form.querySelector('.error-message');
        if (errorDiv) {
            errorDiv.classList.remove('show');
        }
    }

    // Setup du focus trap (accessibilité)
    function setupFocusTrap() {
        focusableElements = modal.querySelectorAll(
            'a[href], button, textarea, input[type="text"], input[type="radio"], input[type="checkbox"], select'
        );
        firstFocusableElement = focusableElements[0];
        lastFocusableElement = focusableElements[focusableElements.length - 1];
    }

    // Gestion du focus trap
    function handleFocusTrap(e) {
        if (e.key !== 'Tab') return;
        
        if (e.shiftKey) {
            if (document.activeElement === firstFocusableElement) {
                lastFocusableElement.focus();
                e.preventDefault();
            }
        } else {
            if (document.activeElement === lastFocusableElement) {
                firstFocusableElement.focus();
                e.preventDefault();
            }
        }
    }

    // Event listeners pour la modale
    modalCloseBtn.addEventListener('click', closeModal);
    
    // Fermeture avec Escape (accessibilité)
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
            closeModal();
        }
        
        // Focus trap quand modale ouverte
        if (!modal.classList.contains('hidden')) {
            handleFocusTrap(e);
        }
    });
    
    // Fermeture en cliquant sur l'overlay
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });
    
    console.log('✅ Modale de confirmation configurée avec succès');
}
