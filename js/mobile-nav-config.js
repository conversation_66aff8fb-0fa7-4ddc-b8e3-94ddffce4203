/**
 * 🚀 CONFIGURATION AVANCÉE MENU MOBILE - SILICON VALLEY EDITION
 * Configuration optimisée pour performances et UX premium
 */

const MOBILE_NAV_CONFIG = {
    // Animations et transitions
    ANIMATION: {
        DURATION: 300,
        EASING: 'cubic-bezier(0.4, 0, 0.2, 1)',
        STAGGER_DELAY: 50, // <PERSON><PERSON><PERSON> entre chaque élément du menu
        REDUCED_MOTION_DURATION: 150 // Pour users avec prefers-reduced-motion
    },

    // Détection de performance
    PERFORMANCE: {
        SLOW_DEVICE_THRESHOLD: 2, // Nombre de cores CPU
        TIMEOUT_FAST: 300,
        TIMEOUT_SLOW: 500,
        DEBOUNCE_DELAY: 150
    },

    // Accessibilité
    ACCESSIBILITY: {
        FOCUS_TRAP: true,
        ARIA_LABELS: {
            TOGGLE_CLOSED: 'Ouvrir le menu de navigation',
            TOGGLE_OPEN: 'Fermer le menu de navigation',
            MENU: 'Menu de navigation mobile'
        },
        KEYBOARD_NAVIGATION: true
    },

    // Responsive breakpoints
    BREAKPOINTS: {
        MOBILE: 768,
        TABLET: 1024
    },

    // Debug et monitoring
    DEBUG: {
        ENABLED: true,
        LOG_PERFORMANCE: true,
        LOG_INTERACTIONS: true
    },

    // Fallback options
    FALLBACK: {
        SIMPLE_TOGGLE: true,
        NO_ANIMATIONS: false,
        BASIC_STYLES: true
    }
};

// Détection des capacités du device
const DEVICE_CAPABILITIES = {
    // Performance
    isSlowDevice: () => {
        return navigator.hardwareConcurrency && 
               navigator.hardwareConcurrency <= MOBILE_NAV_CONFIG.PERFORMANCE.SLOW_DEVICE_THRESHOLD;
    },

    // Connexion
    isSlowConnection: () => {
        const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
        return connection && (
            connection.effectiveType === 'slow-2g' || 
            connection.effectiveType === '2g'
        );
    },

    // Préférences utilisateur
    prefersReducedMotion: () => {
        return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    },

    // Support des fonctionnalités
    supportsBackdropFilter: () => {
        return CSS.supports('backdrop-filter', 'blur(10px)') || 
               CSS.supports('-webkit-backdrop-filter', 'blur(10px)');
    },

    // Device type
    isTouchDevice: () => {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    },

    // iOS Safari detection
    isIOSSafari: () => {
        return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
    }
};

// Configuration adaptative basée sur le device
const ADAPTIVE_CONFIG = {
    getAnimationDuration: () => {
        if (DEVICE_CAPABILITIES.prefersReducedMotion()) {
            return MOBILE_NAV_CONFIG.ANIMATION.REDUCED_MOTION_DURATION;
        }
        if (DEVICE_CAPABILITIES.isSlowDevice()) {
            return MOBILE_NAV_CONFIG.ANIMATION.DURATION * 1.5;
        }
        return MOBILE_NAV_CONFIG.ANIMATION.DURATION;
    },

    getTimeout: () => {
        if (DEVICE_CAPABILITIES.isSlowDevice() || DEVICE_CAPABILITIES.isSlowConnection()) {
            return MOBILE_NAV_CONFIG.PERFORMANCE.TIMEOUT_SLOW;
        }
        return MOBILE_NAV_CONFIG.PERFORMANCE.TIMEOUT_FAST;
    },

    shouldUseBackdropFilter: () => {
        return DEVICE_CAPABILITIES.supportsBackdropFilter() && !DEVICE_CAPABILITIES.isSlowDevice();
    },

    getOptimalEasing: () => {
        if (DEVICE_CAPABILITIES.prefersReducedMotion()) {
            return 'ease';
        }
        return MOBILE_NAV_CONFIG.ANIMATION.EASING;
    }
};

// Export pour utilisation dans le module principal
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { MOBILE_NAV_CONFIG, DEVICE_CAPABILITIES, ADAPTIVE_CONFIG };
}

// Global pour utilisation directe
window.MOBILE_NAV_CONFIG = MOBILE_NAV_CONFIG;
window.DEVICE_CAPABILITIES = DEVICE_CAPABILITIES;
window.ADAPTIVE_CONFIG = ADAPTIVE_CONFIG;

console.log('🚀 Configuration menu mobile Silicon Valley chargée');
