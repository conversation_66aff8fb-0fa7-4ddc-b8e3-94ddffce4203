/**
 * 📊 PERFORMANCE MONITOR - SILICON VALLEY EDITION
 * Monitoring en temps réel des performances du site
 */

class PerformanceMonitor {
    constructor() {
        this.metrics = {
            loadTime: 0,
            firstPaint: 0,
            firstContentfulPaint: 0,
            largestContentfulPaint: 0,
            cumulativeLayoutShift: 0,
            firstInputDelay: 0,
            timeToInteractive: 0
        };
        
        this.thresholds = {
            loadTime: 3000,      // 3s max
            fcp: 1800,           // 1.8s max
            lcp: 2500,           // 2.5s max
            cls: 0.1,            // 0.1 max
            fid: 100             // 100ms max
        };
        
        this.init();
    }

    init() {
        if (typeof window === 'undefined') return;
        
        // Attendre que la page soit chargée
        if (document.readyState === 'complete') {
            this.startMonitoring();
        } else {
            window.addEventListener('load', () => this.startMonitoring());
        }
    }

    startMonitoring() {
        this.measureLoadTime();
        this.measureWebVitals();
        this.setupContinuousMonitoring();
        this.displayResults();
    }

    measureLoadTime() {
        const navigation = performance.getEntriesByType('navigation')[0];
        if (navigation) {
            this.metrics.loadTime = Math.round(navigation.loadEventEnd - navigation.fetchStart);
        }
    }

    measureWebVitals() {
        // First Paint & First Contentful Paint
        const paintEntries = performance.getEntriesByType('paint');
        paintEntries.forEach(entry => {
            if (entry.name === 'first-paint') {
                this.metrics.firstPaint = Math.round(entry.startTime);
            } else if (entry.name === 'first-contentful-paint') {
                this.metrics.firstContentfulPaint = Math.round(entry.startTime);
            }
        });

        // Largest Contentful Paint
        if ('PerformanceObserver' in window) {
            try {
                const lcpObserver = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    const lastEntry = entries[entries.length - 1];
                    this.metrics.largestContentfulPaint = Math.round(lastEntry.startTime);
                });
                lcpObserver.observe({ type: 'largest-contentful-paint', buffered: true });

                // Cumulative Layout Shift
                const clsObserver = new PerformanceObserver((list) => {
                    let clsValue = 0;
                    for (const entry of list.getEntries()) {
                        if (!entry.hadRecentInput) {
                            clsValue += entry.value;
                        }
                    }
                    this.metrics.cumulativeLayoutShift = Math.round(clsValue * 1000) / 1000;
                });
                clsObserver.observe({ type: 'layout-shift', buffered: true });

                // First Input Delay
                const fidObserver = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        this.metrics.firstInputDelay = Math.round(entry.processingStart - entry.startTime);
                    }
                });
                fidObserver.observe({ type: 'first-input', buffered: true });

            } catch (error) {
                console.warn('Performance Observer non supporté:', error);
            }
        }
    }

    setupContinuousMonitoring() {
        // Monitoring des ressources
        this.monitorResourceLoading();
        
        // Monitoring des erreurs JavaScript
        this.monitorJavaScriptErrors();
        
        // Monitoring de la mémoire (si disponible)
        this.monitorMemoryUsage();
    }

    monitorResourceLoading() {
        const resourceObserver = new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
                if (entry.duration > 1000) { // Ressources lentes > 1s
                    console.warn(`🐌 Ressource lente détectée: ${entry.name} (${Math.round(entry.duration)}ms)`);
                }
            }
        });
        
        try {
            resourceObserver.observe({ type: 'resource', buffered: true });
        } catch (error) {
            console.warn('Resource monitoring non supporté');
        }
    }

    monitorJavaScriptErrors() {
        let errorCount = 0;
        
        window.addEventListener('error', (event) => {
            errorCount++;
            console.error(`❌ Erreur JS #${errorCount}:`, {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno
            });
        });

        window.addEventListener('unhandledrejection', (event) => {
            errorCount++;
            console.error(`❌ Promise rejetée #${errorCount}:`, event.reason);
        });
    }

    monitorMemoryUsage() {
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                const usedMB = Math.round(memory.usedJSHeapSize / 1048576);
                const totalMB = Math.round(memory.totalJSHeapSize / 1048576);
                const limitMB = Math.round(memory.jsHeapSizeLimit / 1048576);
                
                if (usedMB > limitMB * 0.8) { // Plus de 80% de la limite
                    console.warn(`🧠 Utilisation mémoire élevée: ${usedMB}MB/${limitMB}MB`);
                }
            }, 30000); // Vérifier toutes les 30s
        }
    }

    getPerformanceScore() {
        let score = 100;
        
        // Pénalités basées sur les seuils
        if (this.metrics.loadTime > this.thresholds.loadTime) {
            score -= 20;
        }
        if (this.metrics.firstContentfulPaint > this.thresholds.fcp) {
            score -= 15;
        }
        if (this.metrics.largestContentfulPaint > this.thresholds.lcp) {
            score -= 20;
        }
        if (this.metrics.cumulativeLayoutShift > this.thresholds.cls) {
            score -= 15;
        }
        if (this.metrics.firstInputDelay > this.thresholds.fid) {
            score -= 10;
        }
        
        return Math.max(0, score);
    }

    getPerformanceGrade() {
        const score = this.getPerformanceScore();
        
        if (score >= 90) return { grade: 'A+', emoji: '🚀', message: 'EXCELLENT - Niveau Silicon Valley!' };
        if (score >= 80) return { grade: 'A', emoji: '⭐', message: 'TRÈS BIEN - Performance optimale' };
        if (score >= 70) return { grade: 'B', emoji: '👍', message: 'BIEN - Quelques optimisations possibles' };
        if (score >= 60) return { grade: 'C', emoji: '⚠️', message: 'MOYEN - Améliorations nécessaires' };
        return { grade: 'D', emoji: '🐌', message: 'FAIBLE - Optimisations urgentes requises' };
    }

    displayResults() {
        setTimeout(() => {
            const { grade, emoji, message } = this.getPerformanceGrade();
            const score = this.getPerformanceScore();
            
            console.log('\n📊 RAPPORT DE PERFORMANCE SILICON VALLEY');
            console.log('==========================================');
            console.log(`${emoji} Note globale: ${grade} (${score}/100)`);
            console.log(`📝 Évaluation: ${message}`);
            console.log('\n📈 MÉTRIQUES DÉTAILLÉES:');
            console.log(`⏱️  Temps de chargement: ${this.metrics.loadTime}ms`);
            console.log(`🎨 First Contentful Paint: ${this.metrics.firstContentfulPaint}ms`);
            console.log(`🖼️  Largest Contentful Paint: ${this.metrics.largestContentfulPaint}ms`);
            console.log(`📐 Cumulative Layout Shift: ${this.metrics.cumulativeLayoutShift}`);
            console.log(`⚡ First Input Delay: ${this.metrics.firstInputDelay}ms`);
            
            // Recommandations
            this.displayRecommendations();
            
            // Exposer les métriques globalement
            window.performanceMetrics = this.metrics;
            window.performanceScore = score;
            
        }, 3000); // Attendre 3s pour avoir toutes les métriques
    }

    displayRecommendations() {
        const recommendations = [];
        
        if (this.metrics.loadTime > this.thresholds.loadTime) {
            recommendations.push('🔧 Optimiser le temps de chargement (compression, CDN)');
        }
        if (this.metrics.firstContentfulPaint > this.thresholds.fcp) {
            recommendations.push('🎨 Améliorer le First Contentful Paint (CSS critique inline)');
        }
        if (this.metrics.largestContentfulPaint > this.thresholds.lcp) {
            recommendations.push('🖼️ Optimiser les images et ressources lourdes');
        }
        if (this.metrics.cumulativeLayoutShift > this.thresholds.cls) {
            recommendations.push('📐 Réduire les décalages de mise en page (dimensions fixes)');
        }
        if (this.metrics.firstInputDelay > this.thresholds.fid) {
            recommendations.push('⚡ Optimiser l\'interactivité (code JavaScript)');
        }
        
        if (recommendations.length > 0) {
            console.log('\n💡 RECOMMANDATIONS:');
            recommendations.forEach(rec => console.log(rec));
        } else {
            console.log('\n✨ Aucune amélioration nécessaire - Performance parfaite!');
        }
    }
}

// Auto-démarrage du monitoring
if (typeof window !== 'undefined') {
    new PerformanceMonitor();
}

// Export pour utilisation externe
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceMonitor;
}
