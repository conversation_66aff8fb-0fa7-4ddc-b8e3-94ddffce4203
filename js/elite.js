/**
 * Elite Portfolio 2.0 - JavaScript Principal
 * T<PERSON><PERSON><PERSON> Campana - Développeur Full-Stack Elite
 * Optimisé pour la performance et l'expérience utilisateur
 */

class ElitePortfolio {
    constructor() {
        this.init();
    }

    init() {
        // Attendre que le DOM soit chargé
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupApp());
        } else {
            this.setupApp();
        }
    }

    setupApp() {
        this.setupNavigation();
        this.setupAnimations();
        this.setupPortfolioFilter();
        this.setupContactForm();
        this.setupSmoothScrolling();
        this.setupMobileMenu();
        this.setupPerformanceOptimizations();
        this.setupAnalytics();
        console.log('🚀 Elite Portfolio 2.0 initialisé avec succès !');
    }

    // Navigation avec effet de scroll
    setupNavigation() {
        const navbar = document.getElementById('navbar');
        let lastScrollTop = 0;

        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            // Ajouter/retirer la classe 'scrolled' pour le style
            if (scrollTop > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }

            // Navigation qui se cache/montre au scroll (optionnel)
            if (scrollTop > lastScrollTop && scrollTop > 100) {
                navbar.style.transform = 'translateY(-100%)';
            } else {
                navbar.style.transform = 'translateY(0)';
            }
            
            lastScrollTop = scrollTop;
        });

        // Highlight des liens actifs
        this.updateActiveNavLink();
        window.addEventListener('scroll', () => this.updateActiveNavLink());
    }

    updateActiveNavLink() {
        const sections = document.querySelectorAll('section[id]');
        const navLinks = document.querySelectorAll('.nav-link');

        let current = '';
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            if (window.scrollY >= sectionTop - 200 && window.scrollY < sectionTop + sectionHeight - 200) {
                current = section.getAttribute('id');
            }
        });

        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${current}`) {
                link.classList.add('active');
            }
        });
    }

    // Animations au scroll
    setupAnimations() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                    
                    // Animation spéciale pour les compteurs
                    if (entry.target.classList.contains('counter')) {
                        this.animateCounter(entry.target);
                    }
                }
            });
        }, observerOptions);

        // Observer tous les éléments avec animation
        const animatedElements = document.querySelectorAll('.fade-in-up, .counter');
        animatedElements.forEach(el => {
            el.classList.add('fade-in-up');
            observer.observe(el);
        });
    }

    // Animation des compteurs
    animateCounter(element) {
        const target = parseInt(element.getAttribute('data-target') || element.textContent);
        const duration = 2000;
        const start = 0;
        const increment = target / (duration / 16);
        let current = start;

        const updateCounter = () => {
            if (current < target) {
                current += increment;
                element.textContent = Math.floor(current);
                requestAnimationFrame(updateCounter);
            } else {
                element.textContent = target;
            }
        };

        updateCounter();
    }

    // Filtres du portfolio
    setupPortfolioFilter() {
        const filterButtons = document.querySelectorAll('.portfolio-filter');
        const portfolioItems = document.querySelectorAll('.portfolio-item');

        filterButtons.forEach(button => {
            button.addEventListener('click', () => {
                const filter = button.getAttribute('data-filter');
                
                // Mettre à jour les boutons actifs
                filterButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                
                // Filtrer les projets
                portfolioItems.forEach(item => {
                    if (filter === 'all' || item.getAttribute('data-category') === filter) {
                        item.style.display = 'block';
                        item.style.animation = 'fadeInUp 0.5s ease';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });
    }

    // Gestion du formulaire de contact
    setupContactForm() {
        const form = document.getElementById('contact-form');
        if (!form) return;

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const submitButton = form.querySelector('button[type="submit"]');
            const originalText = submitButton.innerHTML;
            
            // État de chargement
            submitButton.classList.add('loading');
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Envoi en cours...';
            submitButton.disabled = true;

            try {
                // Collecter les données du formulaire
                const formData = new FormData(form);
                const data = Object.fromEntries(formData);
                
                // Simulation d'envoi (à remplacer par votre API)
                await this.submitContactForm(data);
                
                // Succès
                this.showMessage('success', '✅ Message envoyé avec succès ! Je vous répondrai sous 24h.');
                form.reset();
                
                // Analytics
                this.trackEvent('form_submit', 'contact', 'success');
                
            } catch (error) {
                console.error('Erreur envoi formulaire:', error);
                this.showMessage('error', '❌ Erreur lors de l\'envoi. Veuillez réessayer ou me contacter directement.');
                
                // Analytics
                this.trackEvent('form_submit', 'contact', 'error');
            } finally {
                // Restaurer le bouton
                submitButton.classList.remove('loading');
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
            }
        });
    }

    // Simulation d'envoi de formulaire (à adapter selon vos besoins)
    async submitContactForm(data) {
        // Option 1: Formspree, Netlify Forms, ou service similaire
        // const response = await fetch('https://formspree.io/f/YOUR_FORM_ID', {
        //     method: 'POST',
        //     headers: { 'Content-Type': 'application/json' },
        //     body: JSON.stringify(data)
        // });
        
        // Option 2: EmailJS
        // return emailjs.send('YOUR_SERVICE_ID', 'YOUR_TEMPLATE_ID', data);
        
        // Option 3: Votre propre API
        // const response = await fetch('/api/contact', {
        //     method: 'POST',
        //     headers: { 'Content-Type': 'application/json' },
        //     body: JSON.stringify(data)
        // });
        
        // Simulation pour la démo
        return new Promise((resolve) => {
            setTimeout(resolve, 1500);
        });
    }

    // Affichage des messages
    showMessage(type, message) {
        const existingMessage = document.querySelector('.success-message, .error-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        const messageElement = document.createElement('div');
        messageElement.className = `${type}-message show`;
        messageElement.textContent = message;
        
        const form = document.getElementById('contact-form');
        form.appendChild(messageElement);

        // Auto-hide après 5 secondes
        setTimeout(() => {
            messageElement.classList.remove('show');
            setTimeout(() => messageElement.remove(), 500);
        }, 5000);
    }

    // Scroll fluide
    setupSmoothScrolling() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(anchor.getAttribute('href'));
                if (target) {
                    const offsetTop = target.offsetTop - 80; // Compensation navbar
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });
    }

    // Menu mobile
    setupMobileMenu() {
        // DÉSACTIVÉ - Le menu mobile est maintenant géré par main.js
        // pour éviter les conflits et l'ouverture sur desktop
        Utils.log('info', '📱 Menu mobile géré par PremiumMobileNav - Elite.js désactivé');
        return;
    }

    // Optimisations performance
    setupPerformanceOptimizations() {
        // Lazy loading des images
        if ('loading' in HTMLImageElement.prototype) {
            const images = document.querySelectorAll('img[data-src]');
            images.forEach(img => {
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
            });
        } else {
            // Fallback pour navigateurs non supportés
            this.lazyLoadImages();
        }

        // Préchargement des ressources critiques
        this.preloadCriticalResources();
    }

    lazyLoadImages() {
        const images = document.querySelectorAll('img[data-src]');
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.removeAttribute('data-src');
                    imageObserver.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));
    }

    preloadCriticalResources() {
        // Précharger les fonts critiques
        const fontUrls = [
            'https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap'
        ];

        fontUrls.forEach(url => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'style';
            link.href = url;
            document.head.appendChild(link);
        });
    }

    // Analytics et tracking
    setupAnalytics() {
        // Track les clics sur les projets
        document.querySelectorAll('.portfolio-item a').forEach(link => {
            link.addEventListener('click', (e) => {
                const projectName = e.target.closest('.portfolio-item').querySelector('h3').textContent;
                this.trackEvent('project_click', 'portfolio', projectName);
            });
        });

        // Track les clics sur les CTA
        document.querySelectorAll('a[href="#contact"]').forEach(link => {
            link.addEventListener('click', () => {
                this.trackEvent('cta_click', 'navigation', 'contact');
            });
        });

        // Track le temps passé sur le site
        this.trackTimeOnSite();
    }

    trackEvent(action, category, label = '') {
        // Google Analytics 4
        if (typeof gtag !== 'undefined') {
            gtag('event', action, {
                event_category: category,
                event_label: label
            });
        }

        // Console pour debug
        console.log(`📊 Event tracked: ${action} | ${category} | ${label}`);
    }

    trackTimeOnSite() {
        const startTime = Date.now();
        
        window.addEventListener('beforeunload', () => {
            const timeSpent = Math.round((Date.now() - startTime) / 1000);
            this.trackEvent('time_on_site', 'engagement', `${timeSpent}s`);
        });
    }

    // Utilitaires
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}

// Initialiser l'application
const portfolio = new ElitePortfolio();

// Easter egg pour les développeurs curieux
console.log(`
🚀 Elite Portfolio 2.0 by Thibaut Campana
💼 Développeur Full-Stack & Expert IA
📍 Basé à Marrakech, Maroc
📧 <EMAIL>
💻 Intéressé par mon code ? Contactez-moi !
`);
