// ======================================
// CONFIGURATION API POUR FORMULAIRE DE CONTACT
// Modifiez ces paramètres selon votre backend
// ======================================

const API_CONFIG = {
    // URL de votre endpoint de contact
    // Remplacez par votre vraie URL d'API
    endpoint: 'https://your-api.com/contact', 
    
    // Alternative avec service gratuit comme Formspree
    // endpoint: 'https://formspree.io/f/YOUR_FORM_ID',
    
    // Alternative avec Netlify Forms (si hébergé sur Netlify)
    // Il suffit d'ajouter 'netlify' dans les attributs du form HTML
    
    // Méthode HTTP
    method: 'POST',
    
    // Headers par défaut
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    },
    
    // Timeout en millisecondes
    timeout: 10000,
    
    // Mode de simulation (pour demo)
    simulationMode: true, // Mettre à false pour utiliser une vraie API
    
    // Taux de succès en mode simulation (0.95 = 95% de succès)
    simulationSuccessRate: 0.95
};

// ======================================
// FONCTION D'ENVOI RÉELLE (à personnaliser)
// ======================================

async function sendFormData(formData) {
    if (API_CONFIG.simulationMode) {
        // Mode simulation pour la démo
        return new Promise((resolve) => {
            setTimeout(() => {
                const success = Math.random() < API_CONFIG.simulationSuccessRate;
                resolve({ success, message: success ? 'Message envoyé avec succès' : 'Erreur de simulation' });
            }, 2000);
        });
    }
    
    try {
        // Conversion FormData en objet JavaScript
        const data = {};
        for (const [key, value] of formData.entries()) {
            data[key] = value;
        }
        
        // Appel API réel
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), API_CONFIG.timeout);
        
        const response = await fetch(API_CONFIG.endpoint, {
            method: API_CONFIG.method,
            headers: API_CONFIG.headers,
            body: JSON.stringify(data),
            signal: controller.signal
        });
        
        clearTimeout(timeoutId);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        return { success: true, data: result };
        
    } catch (error) {
        console.error('Erreur API:', error);
        
        if (error.name === 'AbortError') {
            return { success: false, message: 'Timeout: La requête a pris trop de temps' };
        }
        
        return { 
            success: false, 
            message: 'Erreur réseau. Vérifiez votre connexion et réessayez.' 
        };
    }
}

// ======================================
// INSTRUCTIONS D'INTÉGRATION
// ======================================

/*
POUR UTILISER UNE VRAIE API :

1. Modifiez API_CONFIG.endpoint avec votre URL
2. Mettez API_CONFIG.simulationMode = false
3. Adaptez les headers si nécessaire
4. Testez avec votre backend

SERVICES RECOMMANDÉS GRATUITS :
- Formspree: https://formspree.io (gratuit jusqu'à 50 soumissions/mois)
- Netlify Forms: Automatique si hébergé sur Netlify
- EmailJS: https://emailjs.com (gratuit jusqu'à 200 emails/mois)

EXEMPLE AVEC FORMSPREE :
1. Créez un compte sur formspree.io
2. Créez un nouveau formulaire
3. Remplacez l'endpoint par : https://formspree.io/f/YOUR_FORM_ID
4. Mettez simulationMode = false

Le code JavaScript s'adaptera automatiquement !
*/

console.log('📧 Configuration API de contact chargée');
console.log('🔧 Mode simulation:', API_CONFIG.simulationMode ? 'Activé' : 'Désactivé');
